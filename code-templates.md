# 代码模板库

## 基础组件模板

### PhotoCell - 照片单元格
```swift
import UIKit

class PhotoCell: UICollectionViewCell {
    
    // MARK: - UI Elements
    @IBOutlet weak var imageView: UIImageView!
    @IBOutlet weak var selectedOverlay: UIView!
    @IBOutlet weak var selectedCheckmark: UIImageView!
    @IBOutlet weak var durationLabel: UILabel! // 视频时长
    @IBOutlet weak var gradientView: UIView! // 底部渐变
    
    // MARK: - Properties
    var representedAssetIdentifier: String = ""
    
    // MARK: - Lifecycle
    override func awakeFromNib() {
        super.awakeFromNib()
        setupUI()
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        imageView.image = nil
        selectedOverlay.isHidden = true
        durationLabel.isHidden = true
        gradientView.isHidden = true
        representedAssetIdentifier = ""
    }
    
    // MARK: - Setup
    private func setupUI() {
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        
        selectedOverlay.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        selectedOverlay.isHidden = true
        
        selectedCheckmark.image = UIImage(systemName: "checkmark.circle.fill")
        selectedCheckmark.tintColor = .systemBlue
        
        // 视频渐变背景
        let gradient = CAGradientLayer()
        gradient.colors = [UIColor.clear.cgColor, UIColor.black.withAlphaComponent(0.3).cgColor]
        gradient.frame = gradientView.bounds
        gradientView.layer.addSublayer(gradient)
    }
    
    // MARK: - Configuration
    func configure(with asset: PHAsset, isSelected: Bool) {
        representedAssetIdentifier = asset.localIdentifier
        selectedOverlay.isHidden = !isSelected
        
        // 视频时长
        if asset.mediaType == .video {
            durationLabel.isHidden = false
            gradientView.isHidden = false
            durationLabel.text = formatDuration(asset.duration)
        } else {
            durationLabel.isHidden = true
            gradientView.isHidden = true
        }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let formatter = DateComponentsFormatter()
        formatter.unitsStyle = .positional
        formatter.zeroFormattingBehavior = .pad
        formatter.allowedUnits = duration >= 3600 ? [.hour, .minute, .second] : [.minute, .second]
        return formatter.string(from: duration) ?? ""
    }
}
```

### PhotoGridViewController - 主视图控制器框架
```swift
import UIKit
import Photos

class PhotoGridViewController: UIViewController {
    
    // MARK: - UI Elements
    @IBOutlet weak var collectionView: UICollectionView!
    
    // MARK: - Properties
    private let photoCore = PhotoGridCore()
    private var fetchResult: PHFetchResult<PHAsset>!
    private var selectedIndexPaths = Set<IndexPath>()
    private let reuseIdentifier = "PhotoCell"
    
    // 列数配置
    private var currentColumns: Int = 3 {
        didSet {
            updateCollectionViewLayout()
        }
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupGestures()
        checkPermissionAndLoadPhotos()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        updateNavigationBar()
    }
    
    // MARK: - Setup
    private func setupUI() {
        // CollectionView配置
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.prefetchDataSource = self
        collectionView.allowsMultipleSelection = true
        
        // 注册Cell
        let nib = UINib(nibName: "PhotoCell", bundle: nil)
        collectionView.register(nib, forCellWithReuseIdentifier: reuseIdentifier)
        
        // 初始布局
        updateCollectionViewLayout()
    }
    
    private func setupGestures() {
        // 捏合手势
        let pinch = UIPinchGestureRecognizer(target: self, action: #selector(handlePinch(_:)))
        collectionView.addGestureRecognizer(pinch)
        
        // 长按手势
        let longPress = UILongPressGestureRecognizer(target: self, action: #selector(handleLongPress(_:)))
        collectionView.addGestureRecognizer(longPress)
    }
    
    private func updateCollectionViewLayout() {
        let layout = UICollectionViewFlowLayout()
        let spacing: CGFloat = currentColumns == 10 ? 0.5 : (currentColumns == 5 ? 1 : 2)
        let totalSpacing = spacing * CGFloat(currentColumns - 1)
        let itemWidth = (view.bounds.width - totalSpacing) / CGFloat(currentColumns)
        
        layout.itemSize = CGSize(width: itemWidth, height: itemWidth)
        layout.minimumInteritemSpacing = spacing
        layout.minimumLineSpacing = spacing
        layout.sectionInset = .zero
        
        collectionView.setCollectionViewLayout(layout, animated: true)
    }
    
    // MARK: - Photos
    private func checkPermissionAndLoadPhotos() {
        let status = PHPhotoLibrary.authorizationStatus()
        switch status {
        case .authorized:
            loadPhotos()
        case .notDetermined:
            PHPhotoLibrary.requestAuthorization { [weak self] status in
                if status == .authorized {
                    DispatchQueue.main.async {
                        self?.loadPhotos()
                    }
                }
            }
        default:
            showPermissionAlert()
        }
    }
    
    private func loadPhotos() {
        let options = PHFetchOptions()
        options.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: true)]
        fetchResult = PHAsset.fetchAssets(with: options)
        
        DispatchQueue.main.async {
            self.collectionView.reloadData()
            // 滚动到底部（最新照片）
            if self.fetchResult.count > 0 {
                let lastIndexPath = IndexPath(item: self.fetchResult.count - 1, section: 0)
                self.collectionView.scrollToItem(at: lastIndexPath, at: .bottom, animated: false)
            }
        }
    }
    
    // MARK: - Actions
    @objc private func handlePinch(_ gesture: UIPinchGestureRecognizer) {
        if gesture.state == .ended {
            let scale = gesture.scale
            if scale < 0.8 && currentColumns < 10 {
                // 缩小 - 显示更多列
                currentColumns = currentColumns == 3 ? 5 : 10
            } else if scale > 1.2 && currentColumns > 3 {
                // 放大 - 显示更少列
                currentColumns = currentColumns == 10 ? 5 : 3
            }
        }
    }
    
    @objc private func handleLongPress(_ gesture: UILongPressGestureRecognizer) {
        if gesture.state == .began {
            // 进入选择模式
            enterSelectionMode()
        }
    }
    
    // MARK: - Rotation Support
    override func viewWillTransition(to size: CGSize, with coordinator: UIViewControllerTransitionCoordinator) {
        super.viewWillTransition(to: size, with: coordinator)
        
        // 保存当前滚动位置
        let visibleIndexPaths = collectionView.indexPathsForVisibleItems
        let centerPoint = CGPoint(x: collectionView.bounds.midX, y: collectionView.bounds.midY)
        let centerIndexPath = collectionView.indexPathForItem(at: centerPoint)
        
        // 根据新的尺寸调整列数（只做一套尺寸）
        let isLandscape = size.width > size.height
        if isLandscape {
            // 横屏列数: 3/5/10
            switch currentColumns {
            case 3: currentColumns = 3
            case 5: currentColumns = 5
            case 10: currentColumns = 10
            default: break
            }
        } else {
            // 竖屏列数: 3/5/10
            switch currentColumns {
            case 3: currentColumns = 3
            case 5: currentColumns = 5
            case 10: currentColumns = 10
            default: break
            }
        }
        
        // 执行旋转动画
        coordinator.animate(alongsideTransition: { _ in
            self.collectionView.collectionViewLayout.invalidateLayout()
            
            // 保持中心位置
            if let centerIndexPath = centerIndexPath {
                self.collectionView.scrollToItem(at: centerIndexPath, at: .centeredVertically, animated: false)
            }
        }, completion: { _ in
            // 旋转完成后的处理
            self.updateNavigationTitle()
        })
    }
    
    override var supportedInterfaceOrientations: UIInterfaceOrientationMask {
        return UIDevice.current.userInterfaceIdiom == .pad ? .all : .allButUpsideDown
    }
}
```

### PhotoGridCore - 高性能图片加载核心
```swift
import UIKit
import Photos

class PhotoGridCore {
    
    // MARK: - Properties
    private let imageManager = PHCachingImageManager()
    private let cache = NSCache<NSString, UIImage>()
    private let loadingQueue = DispatchQueue(label: "com.mphotos.loading", qos: .userInitiated)
    
    // 请求ID管理
    private var requestIDs = [String: PHImageRequestID]()
    private let requestIDsLock = NSLock()
    
    // MARK: - Initialization
    init() {
        setupCache()
    }
    
    private func setupCache() {
        cache.countLimit = 500 // 最多缓存500张图片
        cache.totalCostLimit = 100 * 1024 * 1024 // 100MB
    }
    
    // MARK: - Public Methods
    @inline(__always)
    func loadThumbnail(for asset: PHAsset, size: CGSize, completion: @escaping (UIImage?) -> Void) {
        let key = cacheKey(for: asset, size: size)
        
        // 检查缓存
        if let cached = cache.object(forKey: key) {
            completion(cached)
            return
        }
        
        // 取消旧请求
        cancelRequest(for: asset.localIdentifier)
        
        // 配置选项
        let options = PHImageRequestOptions()
        options.deliveryMode = .opportunistic
        options.resizeMode = .fast
        options.isNetworkAccessAllowed = false
        
        // 发起新请求
        let requestID = imageManager.requestImage(
            for: asset,
            targetSize: size,
            contentMode: .aspectFill,
            options: options
        ) { [weak self] image, info in
            guard let self = self, let image = image else { return }
            
            // 缓存图片
            self.cache.setObject(image, forKey: key, cost: self.imageCost(image))
            
            // 回调
            DispatchQueue.main.async {
                completion(image)
            }
            
            // 清理请求ID
            self.removeRequestID(for: asset.localIdentifier)
        }
        
        // 保存请求ID
        saveRequestID(requestID, for: asset.localIdentifier)
    }
    
    func preloadImages(for assets: [PHAsset], size: CGSize) {
        imageManager.startCachingImages(
            for: assets,
            targetSize: size,
            contentMode: .aspectFill,
            options: nil
        )
    }
    
    func stopPreloading(for assets: [PHAsset], size: CGSize) {
        imageManager.stopCachingImages(
            for: assets,
            targetSize: size,
            contentMode: .aspectFill,
            options: nil
        )
    }
    
    func clearCache() {
        cache.removeAllObjects()
        imageManager.stopCachingImagesForAllAssets()
        
        requestIDsLock.lock()
        requestIDs.removeAll()
        requestIDsLock.unlock()
    }
    
    // MARK: - Private Methods
    private func cacheKey(for asset: PHAsset, size: CGSize) -> NSString {
        return "\(asset.localIdentifier)-\(Int(size.width))x\(Int(size.height))" as NSString
    }
    
    private func imageCost(_ image: UIImage) -> Int {
        let pixel = Int(image.size.width * image.size.height * image.scale * image.scale)
        return pixel * 4 // 4 bytes per pixel
    }
    
    private func saveRequestID(_ id: PHImageRequestID, for identifier: String) {
        requestIDsLock.lock()
        requestIDs[identifier] = id
        requestIDsLock.unlock()
    }
    
    private func removeRequestID(for identifier: String) {
        requestIDsLock.lock()
        requestIDs.removeValue(forKey: identifier)
        requestIDsLock.unlock()
    }
    
    private func cancelRequest(for identifier: String) {
        requestIDsLock.lock()
        if let requestID = requestIDs[identifier] {
            imageManager.cancelImageRequest(requestID)
            requestIDs.removeValue(forKey: identifier)
        }
        requestIDsLock.unlock()
    }
}
```

### 内存管理模板
```swift
// MARK: - Memory Management
extension PhotoGridViewController {
    
    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        
        print("⚠️ Memory Warning Received")
        
        // 1. 清理图片缓存
        photoCore.clearCache()
        
        // 2. 停止所有预加载
        if let visibleIndexPaths = collectionView.indexPathsForVisibleItems.first {
            let range = max(0, visibleIndexPaths.item - 50)...min(fetchResult.count - 1, visibleIndexPaths.item + 50)
            let assetsToKeep = range.compactMap { fetchResult.object(at: $0) }
            
            // 停止所有缓存
            imageManager.stopCachingImagesForAllAssets()
            
            // 只缓存可见范围
            let size = (collectionView.collectionViewLayout as? UICollectionViewFlowLayout)?.itemSize ?? CGSize(width: 100, height: 100)
            imageManager.startCachingImages(for: assetsToKeep, targetSize: size, contentMode: .aspectFill, options: nil)
        }
        
        // 3. 重新加载可见cell
        collectionView.reloadItems(at: collectionView.indexPathsForVisibleItems)
        
        // 4. 触发垃圾回收
        autoreleasepool {
            // 临时创建并释放对象，触发自动释放池清理
        }
    }
}
```

### 选择管理模板
```swift
// MARK: - Selection Management
extension PhotoGridViewController {
    
    private func enterSelectionMode() {
        navigationItem.rightBarButtonItem = UIBarButtonItem(title: "取消", style: .plain, target: self, action: #selector(exitSelectionMode))
        navigationItem.leftBarButtonItem = UIBarButtonItem(title: "全选", style: .plain, target: self, action: #selector(selectAll))
        
        // 显示工具栏
        showSelectionToolbar()
    }
    
    @objc private func exitSelectionMode() {
        selectedIndexPaths.removeAll()
        collectionView.reloadData()
        
        navigationItem.rightBarButtonItem = nil
        navigationItem.leftBarButtonItem = nil
        hideSelectionToolbar()
    }
    
    @objc private func selectAll() {
        if selectedIndexPaths.count == fetchResult.count {
            // 已全选，执行反选
            selectedIndexPaths.removeAll()
            navigationItem.leftBarButtonItem?.title = "全选"
        } else {
            // 全选
            selectedIndexPaths = Set(0..<fetchResult.count).map { IndexPath(item: $0, section: 0) }
            navigationItem.leftBarButtonItem?.title = "取消全选"
        }
        collectionView.reloadData()
        updateSelectionCount()
    }
    
    private func toggleSelection(at indexPath: IndexPath) {
        if selectedIndexPaths.contains(indexPath) {
            selectedIndexPaths.remove(indexPath)
        } else {
            selectedIndexPaths.insert(indexPath)
        }
        
        collectionView.reloadItems(at: [indexPath])
        updateSelectionCount()
    }
    
    private func updateSelectionCount() {
        title = "已选择 \(selectedIndexPaths.count) 项"
        
        // 更新工具栏按钮状态
        deleteButton.isEnabled = !selectedIndexPaths.isEmpty
        shareButton.isEnabled = !selectedIndexPaths.isEmpty
    }
}
```

### 手势处理模板
```swift
// MARK: - Gesture Handling
extension PhotoGridViewController {
    
    // 连续选择手势
    private func setupPanSelection() {
        let pan = UIPanGestureRecognizer(target: self, action: #selector(handlePanSelection(_:)))
        pan.delegate = self
        collectionView.addGestureRecognizer(pan)
    }
    
    @objc private func handlePanSelection(_ gesture: UIPanGestureRecognizer) {
        guard isSelectionMode else { return }
        
        let location = gesture.location(in: collectionView)
        
        switch gesture.state {
        case .began, .changed:
            if let indexPath = collectionView.indexPathForItem(at: location) {
                if !processedIndexPaths.contains(indexPath) {
                    processedIndexPaths.insert(indexPath)
                    toggleSelection(at: indexPath)
                    
                    // 触觉反馈
                    let impact = UIImpactFeedbackGenerator(style: .light)
                    impact.impactOccurred()
                }
            }
        case .ended, .cancelled:
            processedIndexPaths.removeAll()
        default:
            break
        }
    }
    
    private var processedIndexPaths = Set<IndexPath>()
}
```

### 相簿管理模板
```swift
import UIKit
import Photos

class AlbumManager {
    
    // MARK: - Create Album
    static func createAlbum(with title: String, completion: @escaping (Bool, Error?) -> Void) {
        PHPhotoLibrary.shared().performChanges({
            PHAssetCollectionChangeRequest.creationRequestForAssetCollection(withTitle: title)
        }, completionHandler: completion)
    }
    
    // MARK: - Add Photos to Album
    static func addPhotos(_ assets: [PHAsset], to album: PHAssetCollection, completion: @escaping (Bool, Error?) -> Void) {
        PHPhotoLibrary.shared().performChanges({
            guard let request = PHAssetCollectionChangeRequest(for: album) else { return }
            request.addAssets(assets as NSArray)
        }, completionHandler: completion)
    }
    
    // MARK: - Remove Photos from Album
    static func removePhotos(_ assets: [PHAsset], from album: PHAssetCollection, completion: @escaping (Bool, Error?) -> Void) {
        PHPhotoLibrary.shared().performChanges({
            guard let request = PHAssetCollectionChangeRequest(for: album) else { return }
            request.removeAssets(assets as NSArray)
        }, completionHandler: completion)
    }
    
    // MARK: - Delete Album
    static func deleteAlbum(_ album: PHAssetCollection, completion: @escaping (Bool, Error?) -> Void) {
        PHPhotoLibrary.shared().performChanges({
            PHAssetCollectionChangeRequest.deleteAssetCollections([album] as NSArray)
        }, completionHandler: completion)
    }
    
    // MARK: - Rename Album
    static func renameAlbum(_ album: PHAssetCollection, to newTitle: String, completion: @escaping (Bool, Error?) -> Void) {
        PHPhotoLibrary.shared().performChanges({
            guard let request = PHAssetCollectionChangeRequest(for: album) else { return }
            request.title = newTitle
        }, completionHandler: completion)
    }
}
```

### 导入导出模板
```swift
import UIKit
import Photos

class ImportExportManager: NSObject {
    
    // MARK: - Export
    static func exportPhotos(_ assets: [PHAsset], from viewController: UIViewController) {
        let manager = ImportExportManager()
        manager.performExport(assets, from: viewController)
    }
    
    private func performExport(_ assets: [PHAsset], from viewController: UIViewController) {
        // 准备临时目录
        let tempDir = FileManager.default.temporaryDirectory.appendingPathComponent("MPhotosExport")
        try? FileManager.default.createDirectory(at: tempDir, withIntermediateDirectories: true)
        
        // 导出选项
        let options = PHImageRequestOptions()
        options.version = .current
        options.deliveryMode = .highQualityFormat
        options.isSynchronous = true
        
        var exportURLs: [URL] = []
        
        for (index, asset) in assets.enumerated() {
            autoreleasepool {
                PHImageManager.default().requestImageDataAndOrientation(for: asset, options: options) { data, uti, _, _ in
                    guard let data = data else { return }
                    
                    let filename = "Photo_\(index + 1).jpg"
                    let fileURL = tempDir.appendingPathComponent(filename)
                    
                    try? data.write(to: fileURL)
                    exportURLs.append(fileURL)
                }
            }
        }
        
        // 显示分享界面
        let activityVC = UIActivityViewController(activityItems: exportURLs, applicationActivities: nil)
        viewController.present(activityVC, animated: true)
    }
}

// MARK: - UIDocumentPickerDelegate
extension ImportExportManager: UIDocumentPickerDelegate {
    
    func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
        for url in urls {
            importPhoto(from: url)
        }
    }
    
    private func importPhoto(from url: URL) {
        guard url.startAccessingSecurityScopedResource() else { return }
        defer { url.stopAccessingSecurityScopedResource() }
        
        PHPhotoLibrary.shared().performChanges({
            PHAssetChangeRequest.creationRequestForAssetFromImage(atFileURL: url)
        }) { success, error in
            if success {
                print("Successfully imported photo")
            } else if let error = error {
                print("Error importing photo: \(error)")
            }
        }
    }
}
```

### 搜索筛选模板
```swift
import Photos

struct SearchCriteria {
    var dateRange: DateInterval?
    var mediaType: PHAssetMediaType?
    var minFileSize: Int64?
    var maxFileSize: Int64?
    var isFavorite: Bool?
    var isScreenshot: Bool?
    var isLivePhoto: Bool?
}

class SearchManager {
    
    static func search(with criteria: SearchCriteria) -> PHFetchResult<PHAsset> {
        let options = PHFetchOptions()
        var predicates: [NSPredicate] = []
        
        // 日期范围
        if let dateRange = criteria.dateRange {
            let datePredicate = NSPredicate(
                format: "creationDate >= %@ AND creationDate <= %@",
                dateRange.start as NSDate,
                dateRange.end as NSDate
            )
            predicates.append(datePredicate)
        }
        
        // 媒体类型
        if let mediaType = criteria.mediaType {
            let typePredicate = NSPredicate(format: "mediaType == %d", mediaType.rawValue)
            predicates.append(typePredicate)
        }
        
        // 收藏
        if let isFavorite = criteria.isFavorite {
            let favoritePredicate = NSPredicate(format: "isFavorite == %@", NSNumber(value: isFavorite))
            predicates.append(favoritePredicate)
        }
        
        // 截图
        if criteria.isScreenshot == true {
            let screenshotPredicate = NSPredicate(
                format: "(mediaSubtype & %d) != 0",
                PHAssetMediaSubtype.photoScreenshot.rawValue
            )
            predicates.append(screenshotPredicate)
        }
        
        // Live Photo
        if criteria.isLivePhoto == true {
            let livePhotoPredicate = NSPredicate(
                format: "(mediaSubtype & %d) != 0",
                PHAssetMediaSubtype.photoLive.rawValue
            )
            predicates.append(livePhotoPredicate)
        }
        
        // 组合所有条件
        if !predicates.isEmpty {
            options.predicate = NSCompoundPredicate(andPredicateWithSubpredicates: predicates)
        }
        
        options.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]
        
        return PHAsset.fetchAssets(with: options)
    }
    
    // 文件大小筛选（需要额外处理）
    static func filterByFileSize(assets: PHFetchResult<PHAsset>, min: Int64?, max: Int64?) -> [PHAsset] {
        var filtered: [PHAsset] = []
        
        assets.enumerateObjects { asset, _, _ in
            let resources = PHAssetResource.assetResources(for: asset)
            if let resource = resources.first,
               let fileSize = resource.value(forKey: "fileSize") as? Int64 {
                
                var include = true
                if let min = min, fileSize < min { include = false }
                if let max = max, fileSize > max { include = false }
                
                if include {
                    filtered.append(asset)
                }
            }
        }
        
        return filtered
    }
}
```

### 性能监控模板
```swift
import UIKit

class PerformanceMonitor {
    
    static let shared = PerformanceMonitor()
    
    private var displayLink: CADisplayLink?
    private var lastTimestamp: CFTimeInterval = 0
    private var frameCount = 0
    private var fps: Int = 0
    
    // FPS监控
    func startFPSMonitoring() {
        displayLink = CADisplayLink(target: self, selector: #selector(tick))
        displayLink?.add(to: .main, forMode: .common)
    }
    
    func stopFPSMonitoring() {
        displayLink?.invalidate()
        displayLink = nil
    }
    
    @objc private func tick(link: CADisplayLink) {
        if lastTimestamp == 0 {
            lastTimestamp = link.timestamp
            return
        }
        
        frameCount += 1
        let elapsed = link.timestamp - lastTimestamp
        
        if elapsed >= 1.0 {
            fps = Int(Double(frameCount) / elapsed)
            frameCount = 0
            lastTimestamp = link.timestamp
            
            print("📊 FPS: \(fps)")
            
            if fps < 55 {
                print("⚠️ Low FPS detected: \(fps)")
            }
        }
    }
    
    // 内存监控
    static func currentMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if result == KERN_SUCCESS {
            let usedMemory = Double(info.resident_size) / 1024.0 / 1024.0
            return usedMemory
        }
        return 0
    }
    
    // 性能日志
    static func logPerformance(_ operation: String, block: () -> Void) {
        let startTime = CFAbsoluteTimeGetCurrent()
        let startMemory = currentMemoryUsage()
        
        block()
        
        let endTime = CFAbsoluteTimeGetCurrent()
        let endMemory = currentMemoryUsage()
        
        let duration = (endTime - startTime) * 1000 // 转换为毫秒
        let memoryDelta = endMemory - startMemory
        
        print("""
        📊 Performance Report: \(operation)
        ⏱ Duration: \(String(format: "%.2f", duration))ms
        💾 Memory Change: \(String(format: "%.2f", memoryDelta))MB
        📍 Current Memory: \(String(format: "%.2f", endMemory))MB
        """)
    }
}
```

## 使用说明

1. **复制模板时注意**：
   - 替换类名和变量名以符合你的项目
   - 根据实际需求调整属性和方法
   - 删除不需要的功能

2. **性能优化要点**：
   - 始终使用合适的图片尺寸
   - 及时取消不需要的请求
   - 合理使用缓存
   - 监控内存使用

3. **错误处理**：
   - 每个模板都应该添加适当的错误处理
   - 使用Result类型或throws
   - 提供用户友好的错误提示

4. **扩展建议**：
   - 根据具体需求扩展这些模板
   - 保持代码风格一致
   - 添加适当的注释
