# MPhotos 项目文档导航

## 项目简介

MPhotos 是一个高性能的 iOS 照片管理应用，专注于提供流畅的浏览体验和高效的照片管理功能。本项目采用 AI Agent 驱动的自动化开发流程。

## 文档结构
- `ARCHITECTURE.md` - 系统架构设计（包含技术选型、模块设计、性能策略）
- `API_REFERENCE.md` - iOS系统API使用指南（PhotoKit、UIKit等）
- `CODE_TEMPLATES.md` - 代码模板和示例（可直接复制使用）
- `PROGRESS.md` - 开发进度追踪（当前任务、完成情况）
- `REQUIREMENTS.md` - 完整需求文档（功能需求、性能指标）

## 开发流程
1. 每次开始前说"我要继续开发"
2. Agent会自动读取进度并开始下一个任务
3. 完成后等待编译验证
4. 确认后自动更新进度

## 当前状态
- 当前Phase: Phase 1 - 项目基础搭建
- 当前任务: Task 1.1 - 创建空项目
- 完成进度: 0%
- 预计完成: 2025-01-05

## 快速开始

### 首次使用
```bash
# 1. 创建Xcode项目
我要开始MPhotos项目

# 2. 配置项目
[按照初始化指令操作]

# 3. 开始开发
我要继续开发
```

### 日常开发
```bash
# 继续上次的进度
我要继续开发

# 查看当前进度
查看项目进度

# 遇到编译错误
编译失败：[错误信息]
```

## 项目结构
```
MPhotos/
├── App/                    # 应用入口和配置
├── Core/                   # 核心高性能模块
├── Features/               # 功能模块
│   ├── PhotoGrid/         # 图库功能
│   ├── Albums/            # 相簿功能
│   ├── Cleanup/           # 清理功能（预留）
│   └── Tools/             # 工具功能（预留）
├── Utilities/             # 工具类
├── Resources/             # 资源文件
└── SupportingFiles/       # 配置文件
```

## 技术栈
- **开发语言**: Swift 5.9+
- **UI框架**: UIKit
- **照片框架**: PhotoKit
- **数据库**: Core Data (仅元数据)
- **最低版本**: iOS 16.0

## 性能目标
- 启动时间: < 3秒
- 滚动FPS: > 55
- 内存占用: < 150MB
- 应用大小: < 10MB

## 开发团队
- 开发方式: AI Agent (Claude Sonnet 4)
- 架构设计: Claude Opus 4
- 测试验证: 人工

## 注意事项
1. 每个任务完成后必须验证
2. 保持代码的可运行状态
3. 遇到问题及时记录
4. 定期备份项目

---
最后更新: 2024-12-06