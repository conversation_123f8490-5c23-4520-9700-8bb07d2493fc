// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		B56809E32E40390800E20CED /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B56809C42E40390700E20CED /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B56809CB2E40390700E20CED;
			remoteInfo = MPhotos;
		};
		B56809ED2E40390800E20CED /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B56809C42E40390700E20CED /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B56809CB2E40390700E20CED;
			remoteInfo = MPhotos;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		B56809CC2E40390700E20CED /* MPhotos.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = MPhotos.app; sourceTree = BUILT_PRODUCTS_DIR; };
		B56809E22E40390800E20CED /* MPhotosTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MPhotosTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		B56809EC2E40390800E20CED /* MPhotosUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MPhotosUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		B56809F42E40390800E20CED /* Exceptions for "MPhotos" folder in "MPhotos" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = B56809CB2E40390700E20CED /* MPhotos */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		B56809CE2E40390700E20CED /* MPhotos */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				B56809F42E40390800E20CED /* Exceptions for "MPhotos" folder in "MPhotos" target */,
			);
			path = MPhotos;
			sourceTree = "<group>";
		};
		B56809E52E40390800E20CED /* MPhotosTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MPhotosTests;
			sourceTree = "<group>";
		};
		B56809EF2E40390800E20CED /* MPhotosUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MPhotosUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		B56809C92E40390700E20CED /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B56809DF2E40390800E20CED /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B56809E92E40390800E20CED /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		B56809C32E40390700E20CED = {
			isa = PBXGroup;
			children = (
				B56809CE2E40390700E20CED /* MPhotos */,
				B56809E52E40390800E20CED /* MPhotosTests */,
				B56809EF2E40390800E20CED /* MPhotosUITests */,
				B56809CD2E40390700E20CED /* Products */,
			);
			sourceTree = "<group>";
		};
		B56809CD2E40390700E20CED /* Products */ = {
			isa = PBXGroup;
			children = (
				B56809CC2E40390700E20CED /* MPhotos.app */,
				B56809E22E40390800E20CED /* MPhotosTests.xctest */,
				B56809EC2E40390800E20CED /* MPhotosUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		B56809CB2E40390700E20CED /* MPhotos */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B56809F52E40390800E20CED /* Build configuration list for PBXNativeTarget "MPhotos" */;
			buildPhases = (
				B56809C82E40390700E20CED /* Sources */,
				B56809C92E40390700E20CED /* Frameworks */,
				B56809CA2E40390700E20CED /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				B56809CE2E40390700E20CED /* MPhotos */,
			);
			name = MPhotos;
			packageProductDependencies = (
			);
			productName = MPhotos;
			productReference = B56809CC2E40390700E20CED /* MPhotos.app */;
			productType = "com.apple.product-type.application";
		};
		B56809E12E40390800E20CED /* MPhotosTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B56809FA2E40390800E20CED /* Build configuration list for PBXNativeTarget "MPhotosTests" */;
			buildPhases = (
				B56809DE2E40390800E20CED /* Sources */,
				B56809DF2E40390800E20CED /* Frameworks */,
				B56809E02E40390800E20CED /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B56809E42E40390800E20CED /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				B56809E52E40390800E20CED /* MPhotosTests */,
			);
			name = MPhotosTests;
			packageProductDependencies = (
			);
			productName = MPhotosTests;
			productReference = B56809E22E40390800E20CED /* MPhotosTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		B56809EB2E40390800E20CED /* MPhotosUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B56809FD2E40390800E20CED /* Build configuration list for PBXNativeTarget "MPhotosUITests" */;
			buildPhases = (
				B56809E82E40390800E20CED /* Sources */,
				B56809E92E40390800E20CED /* Frameworks */,
				B56809EA2E40390800E20CED /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B56809EE2E40390800E20CED /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				B56809EF2E40390800E20CED /* MPhotosUITests */,
			);
			name = MPhotosUITests;
			packageProductDependencies = (
			);
			productName = MPhotosUITests;
			productReference = B56809EC2E40390800E20CED /* MPhotosUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		B56809C42E40390700E20CED /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					B56809CB2E40390700E20CED = {
						CreatedOnToolsVersion = 16.4;
					};
					B56809E12E40390800E20CED = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = B56809CB2E40390700E20CED;
					};
					B56809EB2E40390800E20CED = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = B56809CB2E40390700E20CED;
					};
				};
			};
			buildConfigurationList = B56809C72E40390700E20CED /* Build configuration list for PBXProject "MPhotos" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = B56809C32E40390700E20CED;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = B56809CD2E40390700E20CED /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				B56809CB2E40390700E20CED /* MPhotos */,
				B56809E12E40390800E20CED /* MPhotosTests */,
				B56809EB2E40390800E20CED /* MPhotosUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		B56809CA2E40390700E20CED /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B56809E02E40390800E20CED /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B56809EA2E40390800E20CED /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		B56809C82E40390700E20CED /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B56809DE2E40390800E20CED /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B56809E82E40390800E20CED /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		B56809E42E40390800E20CED /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B56809CB2E40390700E20CED /* MPhotos */;
			targetProxy = B56809E32E40390800E20CED /* PBXContainerItemProxy */;
		};
		B56809EE2E40390800E20CED /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B56809CB2E40390700E20CED /* MPhotos */;
			targetProxy = B56809ED2E40390800E20CED /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		B56809F62E40390800E20CED /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5BNRB84BM8;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MPhotos/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.MPCraft.MPhotos;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		B56809F72E40390800E20CED /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5BNRB84BM8;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MPhotos/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.MPCraft.MPhotos;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		B56809F82E40390800E20CED /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 5BNRB84BM8;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		B56809F92E40390800E20CED /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 5BNRB84BM8;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		B56809FB2E40390800E20CED /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5BNRB84BM8;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.MPCraft.MPhotosTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MPhotos.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MPhotos";
			};
			name = Debug;
		};
		B56809FC2E40390800E20CED /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5BNRB84BM8;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.MPCraft.MPhotosTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MPhotos.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MPhotos";
			};
			name = Release;
		};
		B56809FE2E40390800E20CED /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5BNRB84BM8;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.MPCraft.MPhotosUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = MPhotos;
			};
			name = Debug;
		};
		B56809FF2E40390800E20CED /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 5BNRB84BM8;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.MPCraft.MPhotosUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = MPhotos;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		B56809C72E40390700E20CED /* Build configuration list for PBXProject "MPhotos" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B56809F82E40390800E20CED /* Debug */,
				B56809F92E40390800E20CED /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B56809F52E40390800E20CED /* Build configuration list for PBXNativeTarget "MPhotos" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B56809F62E40390800E20CED /* Debug */,
				B56809F72E40390800E20CED /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B56809FA2E40390800E20CED /* Build configuration list for PBXNativeTarget "MPhotosTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B56809FB2E40390800E20CED /* Debug */,
				B56809FC2E40390800E20CED /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B56809FD2E40390800E20CED /* Build configuration list for PBXNativeTarget "MPhotosUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B56809FE2E40390800E20CED /* Debug */,
				B56809FF2E40390800E20CED /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = B56809C42E40390700E20CED /* Project object */;
}
