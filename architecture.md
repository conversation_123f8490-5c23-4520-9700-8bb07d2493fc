# MPhotos 系统架构设计

## 架构概览

MPhotos采用平衡架构设计，在性能关键路径上使用紧耦合实现，在功能扩展点上保持适度灵活性。

```
┌─────────────────────────────────────────────────┐
│                  UI Layer                       │
├─────────────────────────────────────────────────┤
│         PhotoGrid  │  Albums  │  Tools          │
├─────────────────────────────────────────────────┤
│                Core Layer                       │
│    PhotoGridCore (高性能)  │  AlbumManager      │
├─────────────────────────────────────────────────┤
│              System Layer                       │
│    PhotoKit  │  UIKit  │  Core Data            │
└─────────────────────────────────────────────────┘
```

## 设计原则

### 1. 性能优先原则
- 图库滚动必须达到60fps
- 内存占用控制在150MB以内
- 启动时间不超过3秒

### 2. 架构平衡原则
- **高频操作（图片加载）**：直接调用，避免抽象开销
- **低频操作（相簿管理）**：适度解耦，便于维护
- **UI响应**：直接更新，不用通知机制
- **数据共享**：使用单例或静态类，减少传递开销

### 3. AI友好原则
- 每个模块职责清晰
- 避免复杂的依赖关系
- 代码结构扁平化

## 核心模块设计

### PhotoGridCore（高性能图库核心）

```swift
class PhotoGridCore {
    // 直接持有必要组件，不用依赖注入
    private let imageManager = PHCachingImageManager()
    private let cache = NSCache<NSString, UIImage>()
    private var assets: PHFetchResult<PHAsset>?
    
    // 内联关键方法，减少调用开销
    @inline(__always)
    func loadThumbnail(for asset: PHAsset, size: CGSize) -> UIImage?
}
```

**设计要点**：
- 使用`@inline`优化热点方法
- 直接持有PHCachingImageManager
- 同步加载避免闭包开销
- 简单的key-value缓存

### PhotoGridViewController（主界面控制器）

```swift
class PhotoGridViewController: UICollectionViewController {
    // 直接持有核心组件
    private let photoCore = PhotoGridCore()
    private var selectedIndices = Set<IndexPath>()
    
    // 高效的Cell配置
    override func collectionView(_ collectionView: UICollectionView, 
                               cellForItemAt indexPath: IndexPath) -> UICollectionViewCell
}
```

**设计要点**：
- 直接继承UICollectionViewController
- 内部管理选择状态
- 避免过度抽象

### AlbumManager（相簿管理）

```swift
// 相簿管理可以适度解耦
protocol AlbumManagerProtocol {
    func createAlbum(name: String) -> Bool
    func deleteAlbum(_ album: PHAssetCollection) -> Bool
}

class AlbumManager: AlbumManagerProtocol {
    // 实现相簿管理功能
}
```

**设计要点**：
- 低频操作，可以使用协议
- 独立模块，不影响主流程
- 便于后续扩展

## 数据流设计

### 照片加载流（优化路径）

```
用户滚动 -> PhotoGridViewController.cellForItem 
        -> PhotoGridCore.loadThumbnail (检查缓存)
        -> PHCachingImageManager.requestImage
        -> 直接返回UIImage
        -> Cell.imageView.image = image
```

**优化点**：
- 无异步回调
- 无协议查找
- 无通知延迟
- 直接内存访问

### 选择状态管理（直接更新）

```
用户点击 -> toggleSelection(at: indexPath)
        -> selectedIndices.insert/remove
        -> collectionView.reloadItems([indexPath])
        -> updateToolbar()
```

**优化点**：
- 本地状态管理
- 直接UI更新
- 无需事件传递

## 内存管理策略

### 三级质量体系

1. **低质量（200x200）**：快速滑动时
2. **中质量（400x400）**：正常浏览
3. **高质量（屏幕尺寸）**：停止时

### 缓存策略

```swift
// 内存缓存配置
cache.countLimit = 500 // 最多500张
cache.totalCostLimit = 100 * 1024 * 1024 // 100MB

// 缓存key生成
func cacheKey(asset: PHAsset, size: CGSize) -> String {
    return "\(asset.localIdentifier)-\(Int(size.width))x\(Int(size.height))"
}
```

### 内存压力响应

```swift
override func didReceiveMemoryWarning() {
    // 1. 立即停止所有图片加载
    // 2. 清空NSCache
    // 3. 释放不可见cell的图片
    // 4. 强制垃圾回收
}
```

## 性能监控

### 关键指标

- **FPS监控**：使用CADisplayLink
- **内存监控**：task_info API
- **加载时间**：CFAbsoluteTimeGetCurrent

### 监控点

```swift
PerformanceMonitor.measure("Load Thumbnail") {
    image = photoCore.loadThumbnail(for: asset, size: size)
}
```

## 文件组织结构

```
MPhotos/
├── App/                          # 应用配置
│   ├── AppDelegate.swift
│   ├── SceneDelegate.swift
│   └── MainTabBarController.swift
│
├── Core/                         # 核心高性能模块
│   ├── PhotoGridCore.swift      # 图片加载核心
│   ├── MemoryManager.swift      # 内存管理
│   └── Cache.swift              # 缓存实现
│
├── Features/                     # 功能模块
│   ├── PhotoGrid/               # 图库功能
│   │   ├── PhotoGridViewController.swift
│   │   ├── PhotoCell.swift
│   │   └── PhotoCell.xib
│   │
│   ├── Albums/                  # 相簿功能
│   │   ├── AlbumListViewController.swift
│   │   ├── AlbumManager.swift
│   │   └── AlbumCell.swift
│   │
│   ├── Cleanup/                 # 清理功能（预留）
│   │   └── CleanupProtocols.swift
│   │
│   └── Tools/                   # 工具功能（预留）
│       └── ToolsProtocols.swift
│
├── Utilities/                   # 工具类
│   ├── ImportExportManager.swift
│   ├── SearchManager.swift
│   ├── PerformanceMonitor.swift
│   └── Extensions/
│       ├── UIImage+Extensions.swift
│       └── PHAsset+Extensions.swift
│
├── Resources/                   # 资源文件
│   ├── Assets.xcassets
│   └── Localizable.strings
│
└── SupportingFiles/            # 配置文件
    └── Info.plist
```

## 扩展点设计

### 清理功能接口（预留）

```swift
// 验证码/二维码识别
protocol CodeRecognitionProtocol {
    func detectQRCodes(in image: UIImage) -> [String]
    func detectExpiredCodes(in photos: [PhotoModel]) -> [PhotoModel]
}

// 相似照片检测
protocol SimilarityDetectionProtocol {
    func findSimilarPhotos(threshold: Float) -> [[PhotoModel]]
}
```

### 统计功能接口（预留）

```swift
protocol StorageAnalyzerProtocol {
    func analyzeByMonth() -> [MonthlyStorage]
    func analyzeByType() -> [TypeStorage]
    func analyzeBySource() -> [SourceStorage]
}
```

## 技术栈选择理由

### 为什么选择UIKit而非SwiftUI
1. **性能可控**：UIKit的性能优化更成熟
2. **内存管理**：更精确的内存控制
3. **第三方支持**：更多的优化方案和经验

### 为什么直接使用PHCachingImageManager
1. **系统优化**：Apple已经做了大量优化
2. **避免重复造轮子**：减少bug可能性
3. **未来兼容**：跟随系统升级自动获得优化

### 为什么使用紧耦合设计
1. **性能需求**：每次调用节省的几微秒累积起来很可观
2. **代码简单**：更容易理解和调试
3. **AI友好**：减少跨文件的依赖关系

## 性能优化清单

- [x] 使用PHCachingImageManager预加载
- [x] 实现三级图片质量策略
- [x] 内联热点方法
- [x] 避免异步回调开销
- [x] 实现请求取消机制
- [x] 响应内存警告
- [x] 使用autoreleasepool
- [ ] 实现图片解码优化
- [ ] 使用Metal加速（如需要）

## 常见问题解决方案

### Q: 快速滑动时卡顿
A: 检查是否正确取消了不可见cell的请求，是否使用了合适的图片尺寸

### Q: 内存持续增长
A: 检查是否有循环引用，是否正确响应了内存警告

### Q: 加载速度慢
A: 检查是否开启了iCloud下载，是否使用了过大的目标尺寸

## 版本演进计划

### v1.0（当前）
- 基础图库浏览
- 相簿管理
- 性能优化

### v1.1（计划）
- 搜索筛选
- 导入导出
- 存储分析

### v2.0（未来）
- 清理功能
- 智能分类
- 云同步