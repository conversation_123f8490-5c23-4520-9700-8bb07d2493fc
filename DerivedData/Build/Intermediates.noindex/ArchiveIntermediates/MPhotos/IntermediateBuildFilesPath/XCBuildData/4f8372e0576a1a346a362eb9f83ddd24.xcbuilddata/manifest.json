{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath": {"is-mutated": true}, "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation": {"is-mutated": true}, "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app": {"is-mutated": true}, "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos": {"is-mutated": true}, "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath": {"is-mutated": true}, "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>": {"is-command-timestamp": true}, "<TRIGGER: SetMode u+w,go-w,a+rX /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>": {"is-command-timestamp": true}, "<TRIGGER: SetOwnerAndGroup neo:staff /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>": {"is-command-timestamp": true}, "<TRIGGER: Strip /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/_CodeSignature", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache", "<target-MPhotos-****************************************************************--begin-scanning>", "<target-MPhotos-****************************************************************--end>", "<target-MPhotos-****************************************************************--linker-inputs-ready>", "<target-MPhotos-****************************************************************--modules-ready>", "<workspace-Release-iphonesimulator18.5-iphonesimulator--stale-file-removal>"], "outputs": ["<all>"]}, "<target-MPhotos-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/ssu/root.ssu.yaml", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/_CodeSignature", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/thinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/unthinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Base.lproj/LaunchScreen-SBPartialInfo.plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app.dSYM/Contents/Resources/DWARF/MPhotos", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Assets.car", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_signature", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/thinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/unthinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Info.plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/PkgInfo", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent.der", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos Swift Compilation Finished", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_lto.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_dependency_info.dat", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridViewController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PermissionManager.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoCell.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MainTabBarController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/SceneDelegate.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoDataManager.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridTestViewController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridViewController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PermissionManager.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoCell.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MainTabBarController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/SceneDelegate.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoDataManager.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridTestViewController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-master.swiftconstvalues", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftmodule", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftsourceinfo", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.abi.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-Swift.h", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftdoc", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/MPhotos-Swift.h", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-all-target-headers.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-generated-files.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-own-target-headers.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-project-headers.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.DependencyMetadataFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-OutputFileMap.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.LinkFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.SwiftConstValuesFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.SwiftFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_const_extract_protocols.json"], "roots": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath"], "outputs": ["<target-MPhotos-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>"]}, "<workspace-Release-iphonesimulator18.5-iphonesimulator--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos-9765ef73a2a045bb016acc8828e954d1-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<workspace-Release-iphonesimulator18.5-iphonesimulator--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache", "<ClangStatCache /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos.xcodeproj", "signature": "35c0c6bfbcbfe96bef5a20521a45cf73"}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator"]}, "P0:::Gate /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app.dSYM-target-MPhotos-****************************************************************-": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app.dSYM/Contents/Resources/DWARF/MPhotos", "<GenerateDSYMFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app.dSYM/Contents/Resources/DWARF/MPhotos>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app.dSYM/"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos-9765ef73a2a045bb016acc8828e954d1-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-MPhotos-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-MPhotos-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/ssu/root.ssu.yaml", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Metadata.appintents>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.DependencyMetadataFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.SwiftConstValuesFileList"], "outputs": ["<target-MPhotos-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--Barrier-ChangePermissions>", "<target-MPhotos-****************************************************************--will-sign>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-MPhotos-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--Barrier-StripSymbols>", "<target-MPhotos-****************************************************************--will-sign>", "<target-MPhotos-****************************************************************--begin-compiling>", "<SetMode /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>", "<SetOwner /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>"], "outputs": ["<target-MPhotos-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-MPhotos-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-MPhotos-****************************************************************--will-sign>", "<target-MPhotos-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>"], "outputs": ["<target-MPhotos-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-MPhotos-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--Barrier-GenerateStubAPI>", "<target-MPhotos-****************************************************************--will-sign>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-MPhotos-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--ProductPostprocessingTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-MPhotos-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--Barrier-CodeSign>", "<target-MPhotos-****************************************************************--will-sign>", "<target-MPhotos-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>"], "outputs": ["<target-MPhotos-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-MPhotos-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--Barrier-Validate>", "<target-MPhotos-****************************************************************--will-sign>", "<target-MPhotos-****************************************************************--begin-compiling>", "<Touch /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>"], "outputs": ["<target-MPhotos-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-MPhotos-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--Barrier-CopyAside>", "<target-MPhotos-****************************************************************--will-sign>", "<target-MPhotos-****************************************************************--begin-compiling>", "<Strip /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos>"], "outputs": ["<target-MPhotos-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-MPhotos-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-MPhotos-****************************************************************--will-sign>", "<target-MPhotos-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>"], "outputs": ["<target-MPhotos-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-MPhotos-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--CustomTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--GeneratedFilesTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--ProductStructureTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent.der", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/Entitlements-Simulated.plist"], "outputs": ["<target-MPhotos-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-all-target-headers.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-generated-files.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-own-target-headers.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-project-headers.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.hmap"], "outputs": ["<target-MPhotos-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Info.plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/PkgInfo"], "outputs": ["<target-MPhotos-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--RealityAssetsTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-MPhotos-****************************************************************--ModuleMapTaskProducer>", "<target-MPhotos-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-MPhotos-****************************************************************--InfoPlistTaskProducer>", "<target-MPhotos-****************************************************************--SanitizerTaskProducer>", "<target-MPhotos-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-MPhotos-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-MPhotos-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-MPhotos-****************************************************************--TestTargetTaskProducer>", "<target-MPhotos-****************************************************************--TestHostTaskProducer>", "<target-MPhotos-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-MPhotos-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-MPhotos-****************************************************************--DocumentationTaskProducer>", "<target-MPhotos-****************************************************************--CustomTaskProducer>", "<target-MPhotos-****************************************************************--StubBinaryTaskProducer>", "<target-MPhotos-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--start>", "<target-MPhotos-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app"], "outputs": ["<target-MPhotos-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--HeadermapTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-MPhotos-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>"], "outputs": ["<target-MPhotos-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--ProductPostprocessingTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-MPhotos-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-MPhotos-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-MPhotos-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Base.lproj/LaunchScreen-SBPartialInfo.plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Assets.car", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_signature", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Base.lproj/LaunchScreen.storyboardc/", "<MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/unthinned>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos Swift Compilation Finished", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_lto.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_dependency_info.dat", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridViewController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PermissionManager.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoCell.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MainTabBarController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/SceneDelegate.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoDataManager.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridTestViewController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridViewController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PermissionManager.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoCell.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MainTabBarController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/SceneDelegate.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoDataManager.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridTestViewController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-master.swiftconstvalues", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftmodule", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftsourceinfo", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.abi.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-Swift.h", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftdoc", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-OutputFileMap.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.LinkFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.SwiftFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_const_extract_protocols.json"], "outputs": ["<target-MPhotos-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-MPhotos-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-MPhotos-****************************************************************--generated-headers>"]}, "P0:::Gate target-MPhotos-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridViewController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PermissionManager.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoCell.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MainTabBarController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/SceneDelegate.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoDataManager.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridTestViewController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridViewController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PermissionManager.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoCell.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MainTabBarController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/SceneDelegate.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoDataManager.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridTestViewController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-master.swiftconstvalues", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftmodule", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftsourceinfo", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.abi.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-Swift.h", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftdoc", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/MPhotos-Swift.h"], "outputs": ["<target-MPhotos-****************************************************************--swift-generated-headers>"]}, "P0:target-MPhotos-****************************************************************-:Release:AppIntentsSSUTraining": {"tool": "shell", "description": "AppIntentsSSUTraining", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Info.plist", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Metadata.appintents>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.DependencyMetadataFileList", "<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-MPhotos-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/ssu/root.ssu.yaml"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsnltrainingprocessor", "--infoplist-path", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Info.plist", "--temp-dir-path", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/ssu", "--bundle-id", "com.MPCraft.MPhotos", "--product-path", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "--extracted-metadata-path", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Metadata.appintents", "--deployment-postprocessing", "--metadata-file-list", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.DependencyMetadataFileList", "--archive-ssu-assets"], "env": {}, "working-directory": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos", "signature": "6f1ad64e4795e2c8d4065d0751e66a77"}, "P0:target-MPhotos-****************************************************************-:Release:CodeSign /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Info.plist/", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/App/MainTabBarController.swift/", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/AppDelegate.swift/", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Assets.xcassets/", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Base.lproj/LaunchScreen.storyboard/", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Core/PhotoDataManager.swift/", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Features/PhotoGrid/PhotoCell.swift/", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Features/PhotoGrid/PhotoGridTestViewController.swift/", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Features/PhotoGrid/PhotoGridViewController.swift/", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/SceneDelegate.swift/", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Utilities/PermissionManager.swift/", "<target-MPhotos-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-MPhotos-****************************************************************--will-sign>", "<target-MPhotos-****************************************************************--entry>", "<TRIGGER: SetMode u+w,go-w,a+rX /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>", "<TRIGGER: Strip /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/_CodeSignature", "<CodeSign /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>", "<TRIGGER: CodeSign /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>"]}, "P0:target-MPhotos-****************************************************************-:Release:CompileAssetCatalogVariant thinned /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app /Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app /Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Assets.xcassets/", "<MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/thinned>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/thinned", "<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_generated_info.plist_thinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Assets.xcassets", "--compile", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/thinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_generated_info.plist_thinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "16.0", "--platform", "iphonesimulator"], "env": {}, "working-directory": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos", "control-enabled": false, "deps": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_dependencies_thinned"], "deps-style": "dependency-info", "signature": "153df258ac19a2d8d348d7eda6795abd"}, "P0:target-MPhotos-****************************************************************-:Release:CompileAssetCatalogVariant unthinned /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app /Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app /Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Assets.xcassets/", "<MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/unthinned>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/unthinned", "<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_generated_info.plist_unthinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Assets.xcassets", "--compile", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/unthinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_generated_info.plist_unthinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "16.0", "--platform", "iphonesimulator"], "env": {}, "working-directory": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos", "control-enabled": false, "deps": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_dependencies_unthinned"], "deps-style": "dependency-info", "signature": "6f954240a43d09f306bfcc0d0c3d6c1d"}, "P0:target-MPhotos-****************************************************************-:Release:CompileStoryboard /Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Base.lproj/LaunchScreen.storyboard": {"tool": "shell", "description": "CompileStoryboard /Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Base.lproj/LaunchScreen.storyboard", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Base.lproj/LaunchScreen.storyboard", "<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Base.lproj/LaunchScreen-SBPartialInfo.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool", "--errors", "--warnings", "--notices", "--module", "MPhotos", "--output-partial-info-plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Base.lproj/LaunchScreen-SBPartialInfo.plist", "--auto-activate-custom-fonts", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "16.0", "--output-format", "human-readable-text", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Base.lproj/LaunchScreen.storyboard", "--compilation-directory", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Base.lproj"], "env": {"XCODE_DEVELOPER_USR_PATH": "/Applications/Xcode.app/Contents/Developer/usr/bin/.."}, "working-directory": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos", "control-enabled": false, "signature": "b44a5e1e9c62acb486048fe3e3982338"}, "P0:target-MPhotos-****************************************************************-:Release:CopySwiftLibs /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos", "<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-MPhotos-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>"], "deps": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-MPhotos-****************************************************************-:Release:ExtractAppIntentsMetadata": {"tool": "shell", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Features/PhotoGrid/PhotoGridViewController.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Utilities/PermissionManager.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Features/PhotoGrid/PhotoCell.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/App/MainTabBarController.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/AppDelegate.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/SceneDelegate.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Core/PhotoDataManager.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Features/PhotoGrid/PhotoGridTestViewController.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-master.swiftconstvalues", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.DependencyMetadataFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_dependency_info.dat", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.SwiftFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.SwiftConstValuesFileList", "<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-MPhotos-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Metadata.appintents>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor", "--toolchain-dir", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "--module-name", "MPhotos", "--sdk-root", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "--xcode-version", "16F6", "--platform-family", "iOS", "--deployment-target", "16.0", "--bundle-identifier", "com.MPCraft.MPhotos", "--output", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "--target-triple", "arm64-apple-ios16.0-simulator", "--binary-file", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos", "--dependency-file", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_dependency_info.dat", "--stringsdata-file", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "--source-file-list", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.SwiftFileList", "--metadata-file-list", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.DependencyMetadataFileList", "--static-metadata-file-list", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.DependencyStaticMetadataFileList", "--swift-const-vals-list", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.SwiftConstValuesFileList", "--compile-time-extraction", "--deployment-aware-processing", "--validate-assistant-intents", "--no-app-shortcuts-localization"], "env": {}, "working-directory": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos", "signature": "db6fa4e5ca7e603d9f1037b227682fdb"}, "P0:target-MPhotos-****************************************************************-:Release:Gate target-MPhotos-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>"], "outputs": ["<target-MPhotos-****************************************************************--begin-compiling>"]}, "P0:target-MPhotos-****************************************************************-:Release:Gate target-MPhotos-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>"], "outputs": ["<target-MPhotos-****************************************************************--begin-linking>"]}, "P0:target-MPhotos-****************************************************************-:Release:Gate target-MPhotos-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--begin-scanning>"]}, "P0:target-MPhotos-****************************************************************-:Release:Gate target-MPhotos-****************************************************************--end": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--entry>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/ssu/root.ssu.yaml", "<CodeSign /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Base.lproj/LaunchScreen-SBPartialInfo.plist", "<CopySwiftStdlib /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Metadata.appintents>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "<GenerateDSYMFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app.dSYM/Contents/Resources/DWARF/MPhotos>", "<GenerateDSYMFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app.dSYM/Contents/Resources/DWARF/MPhotos>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Assets.car", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_signature", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Base.lproj/LaunchScreen.storyboardc/", "<MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>", "<MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/unthinned>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Info.plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/PkgInfo", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>", "<SetMode /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>", "<SetOwner /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>", "<Strip /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos Swift Compilation Finished", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app", "<Touch /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>", "<Validate /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_lto.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_dependency_info.dat", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridViewController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PermissionManager.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoCell.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MainTabBarController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/SceneDelegate.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoDataManager.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridTestViewController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridViewController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PermissionManager.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoCell.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MainTabBarController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/SceneDelegate.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoDataManager.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridTestViewController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-master.swiftconstvalues", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftmodule", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftsourceinfo", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.abi.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-Swift.h", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftdoc", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/MPhotos-Swift.h", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/MPhotos-Swift.h", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-all-target-headers.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-generated-files.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-own-target-headers.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-project-headers.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.DependencyMetadataFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-OutputFileMap.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.LinkFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.SwiftConstValuesFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.SwiftFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_const_extract_protocols.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app.dSYM/", "<target-MPhotos-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-MPhotos-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-MPhotos-****************************************************************--Barrier-ChangePermissions>", "<target-MPhotos-****************************************************************--Barrier-CodeSign>", "<target-MPhotos-****************************************************************--Barrier-CopyAside>", "<target-MPhotos-****************************************************************--Barrier-GenerateStubAPI>", "<target-MPhotos-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-MPhotos-****************************************************************--Barrier-RegisterProduct>", "<target-MPhotos-****************************************************************--Barrier-StripSymbols>", "<target-MPhotos-****************************************************************--Barrier-Validate>", "<target-MPhotos-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-MPhotos-****************************************************************--CustomTaskProducer>", "<target-MPhotos-****************************************************************--DocumentationTaskProducer>", "<target-MPhotos-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-MPhotos-****************************************************************--GeneratedFilesTaskProducer>", "<target-MPhotos-****************************************************************--HeadermapTaskProducer>", "<target-MPhotos-****************************************************************--InfoPlistTaskProducer>", "<target-MPhotos-****************************************************************--ModuleMapTaskProducer>", "<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--ProductPostprocessingTaskProducer>", "<target-MPhotos-****************************************************************--ProductStructureTaskProducer>", "<target-MPhotos-****************************************************************--RealityAssetsTaskProducer>", "<target-MPhotos-****************************************************************--SanitizerTaskProducer>", "<target-MPhotos-****************************************************************--StubBinaryTaskProducer>", "<target-MPhotos-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-MPhotos-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-MPhotos-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-MPhotos-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-MPhotos-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-MPhotos-****************************************************************--TestHostTaskProducer>", "<target-MPhotos-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-MPhotos-****************************************************************--TestTargetTaskProducer>", "<target-MPhotos-****************************************************************--copy-headers-completion>", "<target-MPhotos-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-MPhotos-****************************************************************--generated-headers>", "<target-MPhotos-****************************************************************--swift-generated-headers>"], "outputs": ["<target-MPhotos-****************************************************************--end>"]}, "P0:target-MPhotos-****************************************************************-:Release:Gate target-MPhotos-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>", "<target-MPhotos-****************************************************************--begin-compiling>"], "outputs": ["<target-MPhotos-****************************************************************--entry>"]}, "P0:target-MPhotos-****************************************************************-:Release:Gate target-MPhotos-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>"], "outputs": ["<target-MPhotos-****************************************************************--immediate>"]}, "P0:target-MPhotos-****************************************************************-:Release:Gate target-MPhotos-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_lto.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_dependency_info.dat", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridViewController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PermissionManager.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoCell.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MainTabBarController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/SceneDelegate.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoDataManager.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridTestViewController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridViewController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PermissionManager.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoCell.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MainTabBarController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/SceneDelegate.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoDataManager.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridTestViewController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-master.swiftconstvalues", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftmodule", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftsourceinfo", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.abi.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-Swift.h", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftdoc", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.LinkFileList"], "outputs": ["<target-MPhotos-****************************************************************--linker-inputs-ready>"]}, "P0:target-MPhotos-****************************************************************-:Release:Gate target-MPhotos-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridViewController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PermissionManager.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoCell.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MainTabBarController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/SceneDelegate.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoDataManager.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridTestViewController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridViewController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PermissionManager.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoCell.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MainTabBarController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/SceneDelegate.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoDataManager.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridTestViewController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-master.swiftconstvalues", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftmodule", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftsourceinfo", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.abi.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-Swift.h", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftdoc", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/MPhotos-Swift.h"], "outputs": ["<target-MPhotos-****************************************************************--modules-ready>"]}, "P0:target-MPhotos-****************************************************************-:Release:Gate target-MPhotos-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/ssu/root.ssu.yaml", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Metadata.appintents>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "<GenerateDSYMFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app.dSYM/Contents/Resources/DWARF/MPhotos>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Assets.car", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_signature", "<MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/unthinned>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent.der", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos Swift Compilation Finished", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_lto.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_dependency_info.dat", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridViewController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PermissionManager.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoCell.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MainTabBarController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/SceneDelegate.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoDataManager.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridTestViewController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridViewController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PermissionManager.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoCell.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MainTabBarController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/SceneDelegate.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoDataManager.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridTestViewController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-master.swiftconstvalues", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftmodule", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftsourceinfo", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.abi.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-Swift.h", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftdoc", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/MPhotos-Swift.h", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.DependencyMetadataFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-OutputFileMap.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.LinkFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.SwiftConstValuesFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.SwiftFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_const_extract_protocols.json", "<target-MPhotos-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-MPhotos-****************************************************************--unsigned-product-ready>"]}, "P0:target-MPhotos-****************************************************************-:Release:Gate target-MPhotos-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-MPhotos-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-MPhotos-****************************************************************--will-sign>"]}, "P0:target-MPhotos-****************************************************************-:Release:GenerateAssetSymbols /Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Assets.xcassets/", "<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Assets.xcassets", "--compile", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "16.0", "--platform", "iphonesimulator", "--bundle-identifier", "com.MPCraft.MPhotos", "--generate-swift-asset-symbols", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos", "control-enabled": false, "signature": "76ba2a5f79c53d69767579b6043d7630"}, "P0:target-MPhotos-****************************************************************-:Release:GenerateDSYMFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app.dSYM /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos": {"tool": "shell", "description": "GenerateDSYMFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app.dSYM /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Info.plist", "<Linked Binary /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftmodule", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<target-MPhotos-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app.dSYM/Contents/Resources/DWARF/MPhotos", "<GenerateDSYMFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app.dSYM/Contents/Resources/DWARF/MPhotos>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/dsymutil", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos", "-o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app.dSYM"], "env": {}, "working-directory": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos", "signature": "14e14db30899bcbf161650123ffaa5c3"}, "P0:target-MPhotos-****************************************************************-:Release:LinkAssetCatalog /Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Assets.xcassets/", "<MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_signature", "<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Assets.car"], "deps": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_dependencies"}, "P0:target-MPhotos-****************************************************************-:Release:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-MPhotos-****************************************************************-:Release:LinkStoryboards": {"tool": "shell", "description": "LinkStoryboards", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Base.lproj/LaunchScreen.storyboardc", "<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Base.lproj/LaunchScreen.storyboardc/"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool", "--errors", "--warnings", "--notices", "--module", "MPhotos", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "16.0", "--output-format", "human-readable-text", "--link", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Base.lproj/LaunchScreen.storyboardc"], "env": {"XCODE_DEVELOPER_USR_PATH": "/Applications/Xcode.app/Contents/Developer/usr/bin/.."}, "working-directory": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos", "control-enabled": false, "signature": "c4a26b26acb2d872e5b4283c2fec7612"}, "P0:target-MPhotos-****************************************************************-:Release:MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "inputs": ["<target-MPhotos-****************************************************************--start>", "<target-MPhotos-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "<MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>", "<TRIGGER: MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>"]}, "P0:target-MPhotos-****************************************************************-:Release:MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/thinned", "inputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/thinned", "<MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/thinned>"]}, "P0:target-MPhotos-****************************************************************-:Release:MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/unthinned", "inputs": ["<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/unthinned", "<MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_output/unthinned>"]}, "P0:target-MPhotos-****************************************************************-:Release:ProcessInfoPlistFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Info.plist /Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Info.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Info.plist /Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Info.plist", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Info.plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Base.lproj/LaunchScreen-SBPartialInfo.plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/assetcatalog_generated_info.plist", "<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Info.plist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/PkgInfo"]}, "P0:target-MPhotos-****************************************************************-:Release:ProcessProductPackaging  /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging  /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/Entitlements-Simulated.plist", "<target-MPhotos-****************************************************************--ProductStructureTaskProducer>", "<target-MPhotos-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent"]}, "P0:target-MPhotos-****************************************************************-:Release:ProcessProductPackagingDER /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent.der", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent", "<target-MPhotos-****************************************************************--ProductStructureTaskProducer>", "<target-MPhotos-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent", "-o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos", "signature": "df3bc6064d38390d75e740e085f9598d"}, "P0:target-MPhotos-****************************************************************-:Release:RegisterExecutionPolicyException /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "<target-MPhotos-****************************************************************--Barrier-CodeSign>", "<target-MPhotos-****************************************************************--will-sign>", "<target-MPhotos-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>"]}, "P0:target-MPhotos-****************************************************************-:Release:SetMode u+w,go-w,a+rX /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app": {"tool": "shell", "description": "SetMode u+w,go-w,a+rX /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "inputs": ["<SetOwner /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>", "<target-MPhotos-****************************************************************--Barrier-StripSymbols>", "<target-MPhotos-****************************************************************--will-sign>", "<target-MPhotos-****************************************************************--entry>", "<TRIGGER: SetOwnerAndGroup neo:staff /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>"], "outputs": ["<SetMode /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>", "<TRIGGER: SetMode u+w,go-w,a+rX /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>"], "args": ["/bin/chmod", "-RH", "u+w,go-w,a+rX", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app"], "env": {}, "working-directory": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos", "signature": "571d6dcb756bb7a40cb4ea014e7017ca"}, "P0:target-MPhotos-****************************************************************-:Release:SetOwnerAndGroup neo:staff /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app": {"tool": "shell", "description": "SetOwnerAndGroup neo:staff /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "inputs": ["<target-MPhotos-****************************************************************--Barrier-StripSymbols>", "<target-MPhotos-****************************************************************--will-sign>", "<target-MPhotos-****************************************************************--entry>", "<TRIGGER: MkDir /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>"], "outputs": ["<SetOwner /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>", "<TRIGGER: SetOwnerAndGroup neo:staff /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>"], "args": ["/usr/sbin/chown", "-RH", "neo:staff", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app"], "env": {}, "working-directory": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos", "signature": "d6013b801a2bb1e403f1eaf4f9d5536c"}, "P0:target-MPhotos-****************************************************************-:Release:Strip /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos": {"tool": "shell", "description": "Strip /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos", "inputs": ["<GenerateDSYMFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app.dSYM/Contents/Resources/DWARF/MPhotos>", "<target-MPhotos-****************************************************************--Barrier-CopyAside>", "<target-MPhotos-****************************************************************--will-sign>", "<target-MPhotos-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos normal>"], "outputs": ["<Strip /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos>", "<TRIGGER: Strip /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/strip", "-D", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos"], "env": {}, "working-directory": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos", "signature": "7e47ea2209473895ab13e083c3e5b609"}, "P0:target-MPhotos-****************************************************************-:Release:SwiftDriver Compilation MPhotos normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation MPhotos normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Features/PhotoGrid/PhotoGridViewController.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Utilities/PermissionManager.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Features/PhotoGrid/PhotoCell.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/App/MainTabBarController.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/AppDelegate.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/SceneDelegate.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Core/PhotoDataManager.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Features/PhotoGrid/PhotoGridTestViewController.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.SwiftFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-OutputFileMap.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_const_extract_protocols.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-generated-files.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-own-target-headers.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-all-target-headers.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-project-headers.hmap", "<ClangStatCache /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache>", "<target-MPhotos-****************************************************************--copy-headers-completion>", "<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos Swift Compilation Finished"]}, "P0:target-MPhotos-****************************************************************-:Release:SymLink /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app ../../InstallationBuildProductsLocation/Applications/MPhotos.app": {"tool": "symlink", "description": "SymLink /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app ../../InstallationBuildProductsLocation/Applications/MPhotos.app", "inputs": ["<target-MPhotos-****************************************************************--start>", "<target-MPhotos-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.app"], "contents": "../../InstallationBuildProductsLocation/Applications/MPhotos.app", "repair-via-ownership-analysis": true}, "P0:target-MPhotos-****************************************************************-:Release:Touch /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app": {"tool": "shell", "description": "Touch /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "<target-MPhotos-****************************************************************--Barrier-Validate>", "<target-MPhotos-****************************************************************--will-sign>", "<target-MPhotos-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app"], "env": {}, "working-directory": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos", "signature": "b27e3910bbfcd3727b2062104f559bcf"}, "P0:target-MPhotos-****************************************************************-:Release:Validate /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/Info.plist", "<target-MPhotos-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-MPhotos-****************************************************************--will-sign>", "<target-MPhotos-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>"], "outputs": ["<Validate /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app>"]}, "P2:::WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos-9765ef73a2a045bb016acc8828e954d1-VFS-iphonesimulator/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos-9765ef73a2a045bb016acc8828e954d1-VFS-iphonesimulator/all-product-headers.yaml", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos-9765ef73a2a045bb016acc8828e954d1-VFS-iphonesimulator/all-product-headers.yaml"]}, "P2:target-MPhotos-****************************************************************-:Release:Copy /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.abi.json", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.abi.json/", "<target-MPhotos-****************************************************************--copy-headers-completion>", "<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.abi.json"]}, "P2:target-MPhotos-****************************************************************-:Release:Copy /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftdoc", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftdoc/", "<target-MPhotos-****************************************************************--copy-headers-completion>", "<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.swiftdoc"]}, "P2:target-MPhotos-****************************************************************-:Release:Copy /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftmodule", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftmodule/", "<target-MPhotos-****************************************************************--copy-headers-completion>", "<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator/MPhotos.swiftmodule/arm64-apple-ios-simulator.swiftmodule"]}, "P2:target-MPhotos-****************************************************************-:Release:Ld /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos normal": {"tool": "shell", "description": "Ld /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos normal", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridViewController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PermissionManager.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoCell.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MainTabBarController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/SceneDelegate.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoDataManager.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridTestViewController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.LinkFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent.der", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator", "<target-MPhotos-****************************************************************--generated-headers>", "<target-MPhotos-****************************************************************--swift-generated-headers>", "<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos", "<Linked Binary /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_lto.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios16.0-simulator", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-<PERSON><PERSON>", "-L/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator", "-L/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator", "-F/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator", "-F/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Release-iphonesimulator", "-filelist", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.LinkFileList", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_lto.o", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-final_output", "-<PERSON><PERSON><PERSON>", "/Applications/MPhotos.app/MPhotos", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_dependency_info.dat", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftmodule", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.app-Simulated.xcent.der", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos"], "env": {}, "working-directory": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos", "deps": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_dependency_info.dat"], "deps-style": "dependency-info", "signature": "a03c4d5ed8825384eb14095f532b4d7b"}, "P2:target-MPhotos-****************************************************************-:Release:SwiftDriver Compilation Requirements MPhotos normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements MPhotos normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Features/PhotoGrid/PhotoGridViewController.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Utilities/PermissionManager.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Features/PhotoGrid/PhotoCell.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/App/MainTabBarController.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/AppDelegate.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/SceneDelegate.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Core/PhotoDataManager.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Features/PhotoGrid/PhotoGridTestViewController.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.SwiftFileList", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-OutputFileMap.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_const_extract_protocols.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-generated-files.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-own-target-headers.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-all-target-headers.hmap", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-project-headers.hmap", "<ClangStatCache /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache>", "<target-MPhotos-****************************************************************--copy-headers-completion>", "<target-MPhotos-****************************************************************--ModuleVerifierTaskProducer>", "<target-MPhotos-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridViewController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PermissionManager.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoCell.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MainTabBarController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/AppDelegate.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/SceneDelegate.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoDataManager.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridTestViewController.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridViewController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PermissionManager.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoCell.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MainTabBarController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/AppDelegate.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/SceneDelegate.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoDataManager.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridTestViewController.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-master.swiftconstvalues", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftmodule", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftsourceinfo", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.abi.json", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-Swift.h", "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.swiftdoc"]}, "P2:target-MPhotos-****************************************************************-:Release:SwiftMergeGeneratedHeaders /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/MPhotos-Swift.h /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/MPhotos-Swift.h /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-Swift.h", "inputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-Swift.h", "<target-MPhotos-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/MPhotos-Swift.h"]}, "P2:target-MPhotos-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/Entitlements-Simulated.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/Entitlements-Simulated.plist", "inputs": ["<target-MPhotos-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/Entitlements-Simulated.plist"]}, "P2:target-MPhotos-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-all-non-framework-target-headers.hmap", "inputs": ["<target-MPhotos-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-all-non-framework-target-headers.hmap"]}, "P2:target-MPhotos-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-all-target-headers.hmap", "inputs": ["<target-MPhotos-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-all-target-headers.hmap"]}, "P2:target-MPhotos-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-generated-files.hmap", "inputs": ["<target-MPhotos-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-generated-files.hmap"]}, "P2:target-MPhotos-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-own-target-headers.hmap", "inputs": ["<target-MPhotos-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-own-target-headers.hmap"]}, "P2:target-MPhotos-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-project-headers.hmap", "inputs": ["<target-MPhotos-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos-project-headers.hmap"]}, "P2:target-MPhotos-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.DependencyMetadataFileList", "inputs": ["<target-MPhotos-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.DependencyMetadataFileList"]}, "P2:target-MPhotos-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.DependencyStaticMetadataFileList", "inputs": ["<target-MPhotos-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.DependencyStaticMetadataFileList"]}, "P2:target-MPhotos-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.hmap", "inputs": ["<target-MPhotos-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/MPhotos.hmap"]}, "P2:target-MPhotos-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-OutputFileMap.json", "inputs": ["<target-MPhotos-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-OutputFileMap.json"]}, "P2:target-MPhotos-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.LinkFileList", "inputs": ["<target-MPhotos-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.LinkFileList"]}, "P2:target-MPhotos-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.SwiftConstValuesFileList", "inputs": ["<target-MPhotos-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.SwiftConstValuesFileList"]}, "P2:target-MPhotos-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.SwiftFileList", "inputs": ["<target-MPhotos-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos.SwiftFileList"]}, "P2:target-MPhotos-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_const_extract_protocols.json", "inputs": ["<target-MPhotos-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos_const_extract_protocols.json"]}}}