{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "9765ef73a2a045bb016acc8828e954d192fc5de22e9796de56986e921c13fc8d"}], "containerPath": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "install", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphonesimulator", "sdk": "iphonesimulator18.5", "sdkVariant": "iphonesimulator", "supportedArchitectures": ["arm64", "x86_64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Products", "derivedDataPath": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Release", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "install", "ASSET_PACK_FOLDER_PATH": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/OnDemandResources", "ASSETCATALOG_COMPILER_FLATTENED_APP_ICON_PATH": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/ProductIcon.png", "COLOR_DIAGNOSTICS": "YES", "DEPLOYMENT_LOCATION": "YES", "DEPLOYMENT_POSTPROCESSING": "YES", "diagnostic_message_length": "80", "DSTROOT": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation", "EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE": "NO", "ENABLE_PREVIEWS": "NO", "ENABLE_SIGNATURE_AGGREGATION": "YES", "ENABLE_XOJIT_PREVIEWS": "YES", "INDEX_ENABLE_DATA_STORE": "NO", "MESSAGES_APPLICATION_EXTENSION_SUPPORT_FOLDER_PATH": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/MessagesApplicationExtensionSupport", "MESSAGES_APPLICATION_SUPPORT_FOLDER_PATH": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/MessagesApplicationSupport", "OBJROOT": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath", "ONLY_ACTIVE_ARCH": "YES", "SHARED_PRECOMPS_DIR": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/PrecompiledHeaders", "SIGNATURE_METADATA_FOLDER_PATH": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/Signatures", "SWIFT_STDLIB_TOOL_UNSIGNED_DESTINATION_DIR": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/SwiftSupport", "SYMROOT": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath", "WATCHKIT_2_SUPPORT_FOLDER_PATH": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/BuildProductsPath/WatchKitSupport2"}}}}, "schemeCommand": "archive", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}