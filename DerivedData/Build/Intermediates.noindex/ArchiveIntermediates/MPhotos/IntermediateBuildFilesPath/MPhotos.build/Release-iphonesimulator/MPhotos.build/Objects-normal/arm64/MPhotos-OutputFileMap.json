{"": {"const-values": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-master.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-master.d", "diagnostics": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MPhotos-master.swiftdeps"}, "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/DerivedSources/GeneratedAssetSymbols.swift": {"index-unit-output-path": "/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/GeneratedAssetSymbols.o"}, "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/App/MainTabBarController.swift": {"index-unit-output-path": "/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MainTabBarController.o", "llvm-bc": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MainTabBarController.bc", "object": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/MainTabBarController.o"}, "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/AppDelegate.swift": {"index-unit-output-path": "/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/AppDelegate.o", "llvm-bc": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/AppDelegate.bc", "object": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/AppDelegate.o"}, "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Core/PhotoDataManager.swift": {"index-unit-output-path": "/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoDataManager.o", "llvm-bc": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoDataManager.bc", "object": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoDataManager.o"}, "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Features/PhotoGrid/PhotoCell.swift": {"index-unit-output-path": "/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoCell.o", "llvm-bc": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoCell.bc", "object": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoCell.o"}, "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Features/PhotoGrid/PhotoGridTestViewController.swift": {"index-unit-output-path": "/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridTestViewController.o", "llvm-bc": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridTestViewController.bc", "object": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridTestViewController.o"}, "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Features/PhotoGrid/PhotoGridViewController.swift": {"index-unit-output-path": "/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridViewController.o", "llvm-bc": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridViewController.bc", "object": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PhotoGridViewController.o"}, "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/SceneDelegate.swift": {"index-unit-output-path": "/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/SceneDelegate.o", "llvm-bc": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/SceneDelegate.bc", "object": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/SceneDelegate.o"}, "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos/Utilities/PermissionManager.swift": {"index-unit-output-path": "/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PermissionManager.o", "llvm-bc": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PermissionManager.bc", "object": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/IntermediateBuildFilesPath/MPhotos.build/Release-iphonesimulator/MPhotos.build/Objects-normal/arm64/PermissionManager.o"}}