---
triple:          'arm64-apple-darwin'
binary-path:     "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/MPhotos/InstallationBuildProductsLocation/Applications/MPhotos.app/MPhotos"
relocations:
  - { offset: 0x9B6C5, size: 0x8, addend: 0x0, symName: '_$sSo44UIViewControllerTransitionCoordinatorContext_pIegg_SoAA_pIeyBy_TR', symObjAddr: 0x118, symBinAddr: 0x100001598, symSize: 0x48 }
  - { offset: 0x9B78F, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerCfETo', symObjAddr: 0x1E50, symBinAddr: 0x1000032D0, symSize: 0x38 }
  - { offset: 0x9B7BE, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerCMa', symObjAddr: 0x1E88, symBinAddr: 0x100003308, symSize: 0x20 }
  - { offset: 0x9B7E1, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC010collectionD0_6layout13sizeForItemAtSo6CGSizeVSo012UICollectionD0C_So0mD6LayoutC10Foundation9IndexPathVtFTo', symObjAddr: 0x1EA8, symBinAddr: 0x100003328, symSize: 0xD0 }
  - { offset: 0x9B813, size: 0x8, addend: 0x0, symName: ___swift_memcpy16_8, symObjAddr: 0x1F78, symBinAddr: 0x1000033F8, symSize: 0xC }
  - { offset: 0x9B827, size: 0x8, addend: 0x0, symName: ___swift_noop_void_return, symObjAddr: 0x1F84, symBinAddr: 0x100003404, symSize: 0x4 }
  - { offset: 0x9B83B, size: 0x8, addend: 0x0, symName: '_$sSo6CGSizeVwet', symObjAddr: 0x1F88, symBinAddr: 0x100003408, symSize: 0x20 }
  - { offset: 0x9B84F, size: 0x8, addend: 0x0, symName: '_$sSo6CGSizeVwst', symObjAddr: 0x1FA8, symBinAddr: 0x100003428, symSize: 0x28 }
  - { offset: 0x9B884, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV6removeyxSgxF10Foundation9IndexPathV_Tg5', symObjAddr: 0x247C, symBinAddr: 0x1000038F0, symSize: 0x220 }
  - { offset: 0x9B8F9, size: 0x8, addend: 0x0, symName: '_$sSh8_VariantV6insertySb8inserted_x17memberAfterInserttxnF10Foundation9IndexPathV_Tg5', symObjAddr: 0x269C, symBinAddr: 0x100003B10, symSize: 0x244 }
  - { offset: 0x9B978, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV9insertNew_2at8isUniqueyxn_s10_HashTableV6BucketVSbtF10Foundation9IndexPathV_Tg5', symObjAddr: 0x28E0, symBinAddr: 0x100003D54, symSize: 0x1F8 }
  - { offset: 0x9B9F9, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV4copyyyF10Foundation9IndexPathV_Tg5', symObjAddr: 0x2AD8, symBinAddr: 0x100003F4C, symSize: 0x210 }
  - { offset: 0x9BA84, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV13copyAndResize8capacityySi_tF10Foundation9IndexPathV_Tg5', symObjAddr: 0x2CE8, symBinAddr: 0x10000415C, symSize: 0x314 }
  - { offset: 0x9BB1E, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV6resize8capacityySi_tF10Foundation9IndexPathV_Tg5', symObjAddr: 0x2FFC, symBinAddr: 0x100004470, symSize: 0x358 }
  - { offset: 0x9BBE0, size: 0x8, addend: 0x0, symName: '_$ss10_NativeSetV7_delete2atys10_HashTableV6BucketV_tF10Foundation9IndexPathV_Tg5', symObjAddr: 0x3354, symBinAddr: 0x1000047C8, symSize: 0x274 }
  - { offset: 0x9BC49, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC010collectionD0_6layout13sizeForItemAtSo6CGSizeVSo012UICollectionD0C_So0mD6LayoutC10Foundation9IndexPathVtFTf4dnnn_n', symObjAddr: 0x35C8, symBinAddr: 0x100004A3C, symSize: 0x164 }
  - { offset: 0x9BD62, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC17requestPermission33_9A0DCD7D969D51583AC72A231C48074DLLyyFyAA0G7ManagerC0G6StatusOcfU_TA', symObjAddr: 0x3750, symBinAddr: 0x100004BC4, symSize: 0x8 }
  - { offset: 0x9BD76, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC019requestPhotoLibraryB010completionyyAC0B6StatusOc_tFySo015PHAuthorizationH0VcfU_TA', symObjAddr: 0x37A8, symBinAddr: 0x100004C1C, symSize: 0xC }
  - { offset: 0x9BD8A, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x37B4, symBinAddr: 0x100004C28, symSize: 0x10 }
  - { offset: 0x9BD9E, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x37C4, symBinAddr: 0x100004C38, symSize: 0x8 }
  - { offset: 0x9BDB2, size: 0x8, addend: 0x0, symName: '_$sSo17OS_dispatch_queueCMa', symObjAddr: 0x37CC, symBinAddr: 0x100004C40, symSize: 0x44 }
  - { offset: 0x9BDC6, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC04loadB4Data33_9A0DCD7D969D51583AC72A231C48074DLLyyFyyYbcfU_TA', symObjAddr: 0x3810, symBinAddr: 0x100004C84, symSize: 0x8 }
  - { offset: 0x9BDDA, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledName, symObjAddr: 0x3818, symBinAddr: 0x100004C8C, symSize: 0x44 }
  - { offset: 0x9BDEE, size: 0x8, addend: 0x0, symName: '_$sSay8Dispatch0A13WorkItemFlagsVGSayxGSTsWl', symObjAddr: 0x385C, symBinAddr: 0x100004CD0, symSize: 0x48 }
  - { offset: 0x9BE02, size: 0x8, addend: 0x0, symName: ___swift_instantiateConcreteTypeFromMangledNameAbstract, symObjAddr: 0x38A4, symBinAddr: 0x100004D18, symSize: 0x48 }
  - { offset: 0x9BE16, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC04loadB4Data33_9A0DCD7D969D51583AC72A231C48074DLLyyFyyYbcfU_yyScMYccfU_TA', symObjAddr: 0x3918, symBinAddr: 0x100004D8C, symSize: 0x8 }
  - { offset: 0x9BE2A, size: 0x8, addend: 0x0, symName: ___swift_project_boxed_opaque_existential_0, symObjAddr: 0x3920, symBinAddr: 0x100004D94, symSize: 0x24 }
  - { offset: 0x9BE3E, size: 0x8, addend: 0x0, symName: ___swift_destroy_boxed_opaque_existential_0, symObjAddr: 0x3944, symBinAddr: 0x100004DB8, symSize: 0x20 }
  - { offset: 0x9BE52, size: 0x8, addend: 0x0, symName: '_$s7MPhotos9PhotoCellC20updateSelectionState33_9EB19863FBF4F3179AEC9D17D32FCC60LLyyFyycfU_TA', symObjAddr: 0x3988, symBinAddr: 0x100004DFC, symSize: 0x8 }
  - { offset: 0x9BE66, size: 0x8, addend: 0x0, symName: '_$s7MPhotos9PhotoCellC20updateSelectionState33_9EB19863FBF4F3179AEC9D17D32FCC60LLyyFySbcfU0_TA', symObjAddr: 0x3990, symBinAddr: 0x100004E04, symSize: 0x8 }
  - { offset: 0x9BE7A, size: 0x8, addend: 0x0, symName: '_$s10Foundation9IndexPathVSgWOh', symObjAddr: 0x3998, symBinAddr: 0x100004E0C, symSize: 0x40 }
  - { offset: 0x9BE8E, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC18viewWillTransition2to4withySo6CGSizeV_So06UIVieweH11Coordinator_ptFySo0lehM7Context_pcfU_TA', symObjAddr: 0x3A18, symBinAddr: 0x100004E8C, symSize: 0x20 }
  - { offset: 0x9BEC0, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x3AB8, symBinAddr: 0x100004F2C, symSize: 0x2C }
  - { offset: 0x9BED4, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x3AE4, symBinAddr: 0x100004F58, symSize: 0x2C }
  - { offset: 0x9BEE8, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas20_SwiftNewtypeWrapperSCSYWb', symObjAddr: 0x3B10, symBinAddr: 0x100004F84, symSize: 0x2C }
  - { offset: 0x9BEFC, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas20_SwiftNewtypeWrapperSCs35_HasCustomAnyHashableRepresentationPWb', symObjAddr: 0x3B3C, symBinAddr: 0x100004FB0, symSize: 0x2C }
  - { offset: 0x9BF10, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyaSHSCSQWb', symObjAddr: 0x3B68, symBinAddr: 0x100004FDC, symSize: 0x2C }
  - { offset: 0x9BF24, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaSHSCSQWb', symObjAddr: 0x3B94, symBinAddr: 0x100005008, symSize: 0x2C }
  - { offset: 0x9C005, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC11viewDidLoadyyFTo', symObjAddr: 0x0, symBinAddr: 0x100001480, symSize: 0xB8 }
  - { offset: 0x9C138, size: 0x8, addend: 0x0, symName: '_$sSh8containsySbxF10Foundation9IndexPathV_Tg5', symObjAddr: 0x14B8, symBinAddr: 0x100002938, symSize: 0x174 }
  - { offset: 0x9C1E7, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas21_ObjectiveCBridgeableSCsACP016_forceBridgeFromE1C_6resulty01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x220C, symBinAddr: 0x100003680, symSize: 0x14 }
  - { offset: 0x9C203, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas21_ObjectiveCBridgeableSCsACP024_conditionallyBridgeFromE1C_6resultSb01_E5CTypeQz_xSgztFZTW', symObjAddr: 0x2220, symBinAddr: 0x100003694, symSize: 0x18 }
  - { offset: 0x9C21F, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeyas35_HasCustomAnyHashableRepresentationSCsACP03_toghI0s0hI0VSgyFTW', symObjAddr: 0x2304, symBinAddr: 0x100003778, symSize: 0x84 }
  - { offset: 0x9C23B, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyas35_HasCustomAnyHashableRepresentationSCsACP03_tofgH0s0gH0VSgyFTW', symObjAddr: 0x23F8, symBinAddr: 0x10000386C, symSize: 0x84 }
  - { offset: 0x9C2CE, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC14viewWillAppearyySbFTo', symObjAddr: 0xB8, symBinAddr: 0x100001538, symSize: 0x60 }
  - { offset: 0x9C490, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC18viewWillTransition2to4withySo6CGSizeV_So06UIVieweH11Coordinator_ptFTo', symObjAddr: 0x160, symBinAddr: 0x1000015E0, symSize: 0x114 }
  - { offset: 0x9C4E0, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC7setupUI33_9A0DCD7D969D51583AC72A231C48074DLLyyF', symObjAddr: 0x274, symBinAddr: 0x1000016F4, symSize: 0x1E8 }
  - { offset: 0x9C548, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC015setupCollectionD033_9A0DCD7D969D51583AC72A231C48074DLLyyF', symObjAddr: 0x45C, symBinAddr: 0x1000018DC, symSize: 0x1B4 }
  - { offset: 0x9C65B, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC22calculateThumbnailSize33_9A0DCD7D969D51583AC72A231C48074DLLyyF', symObjAddr: 0x610, symBinAddr: 0x100001A90, symSize: 0x234 }
  - { offset: 0x9C7EA, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC04loadB4Data33_9A0DCD7D969D51583AC72A231C48074DLLyyF', symObjAddr: 0x844, symBinAddr: 0x100001CC4, symSize: 0x238 }
  - { offset: 0x9C838, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC04loadB4Data33_9A0DCD7D969D51583AC72A231C48074DLLyyFyyYbcfU_', symObjAddr: 0xA7C, symBinAddr: 0x100001EFC, symSize: 0x220 }
  - { offset: 0x9C8A1, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC04loadB4Data33_9A0DCD7D969D51583AC72A231C48074DLLyyFyyYbcfU_yyScMYccfU_', symObjAddr: 0xC9C, symBinAddr: 0x10000211C, symSize: 0x174 }
  - { offset: 0x9CA19, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC17requestPermission33_9A0DCD7D969D51583AC72A231C48074DLLyyF', symObjAddr: 0xE10, symBinAddr: 0x100002290, symSize: 0x240 }
  - { offset: 0x9CAE3, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC17requestPermission33_9A0DCD7D969D51583AC72A231C48074DLLyyFyAA0G7ManagerC0G6StatusOcfU_', symObjAddr: 0x1050, symBinAddr: 0x1000024D0, symSize: 0xBC }
  - { offset: 0x9CB49, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC19toggleSelectionMode33_9A0DCD7D969D51583AC72A231C48074DLLyyFTo', symObjAddr: 0x110C, symBinAddr: 0x10000258C, symSize: 0x94 }
  - { offset: 0x9CC29, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC010collectionD0_22numberOfItemsInSectionSiSo012UICollectionD0C_SitFTo', symObjAddr: 0x11A0, symBinAddr: 0x100002620, symSize: 0x20 }
  - { offset: 0x9CC41, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC010collectionD0_22numberOfItemsInSectionSiSo012UICollectionD0C_SitFTo', symObjAddr: 0x11A0, symBinAddr: 0x100002620, symSize: 0x20 }
  - { offset: 0x9CC70, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC010collectionD0_22numberOfItemsInSectionSiSo012UICollectionD0C_SitFTo', symObjAddr: 0x11A0, symBinAddr: 0x100002620, symSize: 0x20 }
  - { offset: 0x9CD2C, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC010collectionD0_13cellForItemAtSo012UICollectionD4CellCSo0kD0C_10Foundation9IndexPathVtF', symObjAddr: 0x11C0, symBinAddr: 0x100002640, symSize: 0x2F8 }
  - { offset: 0x9CEB6, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC010collectionD0_13cellForItemAtSo012UICollectionD4CellCSo0kD0C_10Foundation9IndexPathVtFTo', symObjAddr: 0x162C, symBinAddr: 0x100002AAC, symSize: 0xA8 }
  - { offset: 0x9CEF8, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC010collectionD0_15didSelectItemAtySo012UICollectionD0C_10Foundation9IndexPathVtF', symObjAddr: 0x16D4, symBinAddr: 0x100002B54, symSize: 0x43C }
  - { offset: 0x9D162, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC010collectionD0_15didSelectItemAtySo012UICollectionD0C_10Foundation9IndexPathVtFTo', symObjAddr: 0x1B10, symBinAddr: 0x100002F90, symSize: 0xA0 }
  - { offset: 0x9D199, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC010collectionD6LayoutACSo012UICollectiondG0C_tcfcTo', symObjAddr: 0x1BB0, symBinAddr: 0x100003030, symSize: 0x88 }
  - { offset: 0x9D1D6, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC7nibName6bundleACSSSg_So8NSBundleCSgtcfc', symObjAddr: 0x1C38, symBinAddr: 0x1000030B8, symSize: 0xC0 }
  - { offset: 0x9D217, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC7nibName6bundleACSSSg_So8NSBundleCSgtcfcTo', symObjAddr: 0x1CF8, symBinAddr: 0x100003178, symSize: 0x5C }
  - { offset: 0x9D22B, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x1D54, symBinAddr: 0x1000031D4, symSize: 0xA8 }
  - { offset: 0x9D25E, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x1DFC, symBinAddr: 0x10000327C, symSize: 0x24 }
  - { offset: 0x9D272, size: 0x8, addend: 0x0, symName: '_$s7MPhotos23PhotoGridViewControllerCfD', symObjAddr: 0x1E20, symBinAddr: 0x1000032A0, symSize: 0x30 }
  - { offset: 0x9D29C, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaSYSCSY8rawValue03RawF0QzvgTW', symObjAddr: 0x23D0, symBinAddr: 0x100003844, symSize: 0x28 }
  - { offset: 0x9D416, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC6sharedACvpZ', symObjAddr: 0x8888, symBinAddr: 0x10001AD70, symSize: 0x0 }
  - { offset: 0x9D547, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC6shared_WZ', symObjAddr: 0x0, symBinAddr: 0x1000050AC, symSize: 0x28 }
  - { offset: 0x9D577, size: 0x8, addend: 0x0, symName: '_$sSo21PHAuthorizationStatusVIegy_ABIeyBy_TR', symObjAddr: 0x294, symBinAddr: 0x100005340, symSize: 0x3C }
  - { offset: 0x9D58F, size: 0x8, addend: 0x0, symName: '_$sSo13UIAlertActionCIegg_ABIeyBy_TR', symObjAddr: 0x4D0, symBinAddr: 0x10000557C, symSize: 0x50 }
  - { offset: 0x9D5A7, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerCMa', symObjAddr: 0x73C, symBinAddr: 0x1000057E8, symSize: 0x20 }
  - { offset: 0x9D634, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC05checkB11OnAppLaunch4from10completionySo16UIViewControllerC_yAC0B6StatusOctF0102$s7MPhotos13SceneDelegateC27checkPhotoLibraryPermission33_2145FA1267F93B6F4563514BDBB8947ELL4fromySo16jk10C_tFyAA0G7c4C0G6L5OcfU_Tf1ncn_n', symObjAddr: 0x75C, symBinAddr: 0x100005808, symSize: 0x458 }
  - { offset: 0x9DA20, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC05checkB11OnAppLaunch4from10completionySo16UIViewControllerC_yAC0B6StatusOctFyAJcfU_0102$s7MPhotos13SceneDelegateC27checkPhotoLibraryPermission33_2145FA1267F93B6F4563514BDBB8947ELL4fromySo16jk10C_tFyAA0G7c4C0G6L5OcfU_Tf1nnnc_n', symObjAddr: 0xBB4, symBinAddr: 0x100005C60, symSize: 0xC0 }
  - { offset: 0x9DA65, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC05checkB11OnAppLaunch4from10completionySo16UIViewControllerC_yAC0B6StatusOctFyAJcfU_', symObjAddr: 0xC74, symBinAddr: 0x100005D20, symSize: 0xD4 }
  - { offset: 0x9DA9E, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC05checkB11OnAppLaunch4from10completionySo16UIViewControllerC_yAC0B6StatusOctFyAJcfU_TA', symObjAddr: 0xDE4, symBinAddr: 0x100005E4C, symSize: 0xC }
  - { offset: 0x9DAB2, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0xE28, symBinAddr: 0x100005E84, symSize: 0x10 }
  - { offset: 0x9DAC6, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0xE38, symBinAddr: 0x100005E94, symSize: 0x8 }
  - { offset: 0x9DADA, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC019requestPhotoLibraryB010completionyyAC0B6StatusOc_tFySo015PHAuthorizationH0VcfU_yyScMYccfU_TA', symObjAddr: 0xEB0, symBinAddr: 0x100005EC8, symSize: 0xC }
  - { offset: 0x9DAF9, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4findys10_HashTableV6BucketV6bucket_Sb5foundtxSHRzlFSo38UIApplicationOpenExternalURLOptionsKeya_Tg5', symObjAddr: 0xF4C, symBinAddr: 0x100005ED4, symSize: 0x80 }
  - { offset: 0x9DB98, size: 0x8, addend: 0x0, symName: '_$ss22__RawDictionaryStorageC4find_9hashValues10_HashTableV6BucketV6bucket_Sb5foundtx_SitSHRzlFSo38UIApplicationOpenExternalURLOptionsKeya_Tg5', symObjAddr: 0xFCC, symBinAddr: 0x100005F54, symSize: 0x178 }
  - { offset: 0x9DCEB, size: 0x8, addend: 0x0, symName: '_$s10Foundation3URLVSgWOh', symObjAddr: 0x1424, symBinAddr: 0x1000063AC, symSize: 0x40 }
  - { offset: 0x9DCFF, size: 0x8, addend: 0x0, symName: '_$sSo38UIApplicationOpenExternalURLOptionsKeya_yptWOc', symObjAddr: 0x14A4, symBinAddr: 0x1000063EC, symSize: 0x48 }
  - { offset: 0x9DD13, size: 0x8, addend: 0x0, symName: '_$sypWOb', symObjAddr: 0x14EC, symBinAddr: 0x100006434, symSize: 0x10 }
  - { offset: 0x9DD27, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC04showB11DeniedAlert4fromySo16UIViewControllerC_tFySo13UIAlertActionCcfU_TA', symObjAddr: 0x14FC, symBinAddr: 0x100006444, symSize: 0x4 }
  - { offset: 0x9DD3F, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC04showB11DeniedAlert4fromySo16UIViewControllerC_tFySo13UIAlertActionCcfU_TA', symObjAddr: 0x14FC, symBinAddr: 0x100006444, symSize: 0x4 }
  - { offset: 0x9DD53, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC04showB11DeniedAlert4fromySo16UIViewControllerC_tFySo13UIAlertActionCcfU_TA', symObjAddr: 0x14FC, symBinAddr: 0x100006444, symSize: 0x4 }
  - { offset: 0x9DDE4, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC019requestPhotoLibraryB010completionyyAC0B6StatusOc_tFySo015PHAuthorizationH0VcfU_', symObjAddr: 0x28, symBinAddr: 0x1000050D4, symSize: 0x1E4 }
  - { offset: 0x9DE68, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC019requestPhotoLibraryB010completionyyAC0B6StatusOc_tFySo015PHAuthorizationH0VcfU_yyScMYccfU_', symObjAddr: 0x20C, symBinAddr: 0x1000052B8, symSize: 0x88 }
  - { offset: 0x9DF9D, size: 0x8, addend: 0x0, symName: '_$sSD17dictionaryLiteralSDyxq_Gx_q_td_tcfCSo38UIApplicationOpenExternalURLOptionsKeya_ypTt0g5Tf4g_n', symObjAddr: 0x1144, symBinAddr: 0x1000060CC, symSize: 0xF0 }
  - { offset: 0x9E1B7, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC04showB11DeniedAlert4fromySo16UIViewControllerC_tF', symObjAddr: 0x2D0, symBinAddr: 0x10000537C, symSize: 0x200 }
  - { offset: 0x9E272, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC22showLimitedAccessAlert4fromySo16UIViewControllerC_tF', symObjAddr: 0x520, symBinAddr: 0x1000055CC, symSize: 0x20C }
  - { offset: 0x9E307, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerCfD', symObjAddr: 0x72C, symBinAddr: 0x1000057D8, symSize: 0x10 }
  - { offset: 0x9E3E8, size: 0x8, addend: 0x0, symName: '_$s7MPhotos17PermissionManagerC15openAppSettings33_7A20A667FEBED227F44F8689CCAF6561LLyyFTf4d_n', symObjAddr: 0x1234, symBinAddr: 0x1000061BC, symSize: 0x1F0 }
  - { offset: 0x9E8F6, size: 0x8, addend: 0x0, symName: '_$s7MPhotos9PhotoCellCfETo', symObjAddr: 0x1800, symBinAddr: 0x100007C28, symSize: 0x78 }
  - { offset: 0x9E925, size: 0x8, addend: 0x0, symName: '_$s7MPhotos9PhotoCellCMa', symObjAddr: 0x1878, symBinAddr: 0x100007CA0, symSize: 0x20 }
  - { offset: 0x9E939, size: 0x8, addend: 0x0, symName: '_$s7MPhotos9PhotoCellC9configure4with10targetSizeySo7PHAssetC_So6CGSizeVtFySo7UIImageCSgcfU_TA', symObjAddr: 0x18E8, symBinAddr: 0x100007D10, symSize: 0x8 }
  - { offset: 0x9E96E, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x1ABC, symBinAddr: 0x100007E98, symSize: 0x10 }
  - { offset: 0x9E982, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x1ACC, symBinAddr: 0x100007EA8, symSize: 0x8 }
  - { offset: 0x9E996, size: 0x8, addend: 0x0, symName: '_$s7MPhotos9PhotoCellC20updateSelectionState33_9EB19863FBF4F3179AEC9D17D32FCC60LLyyFySbcfU0_yycfU_TA', symObjAddr: 0x1B00, symBinAddr: 0x100007ED4, symSize: 0x48 }
  - { offset: 0x9E9EF, size: 0x8, addend: 0x0, symName: '_$s7MPhotos9PhotoCellC5frameACSo6CGRectV_tcfc', symObjAddr: 0x0, symBinAddr: 0x100006464, symSize: 0x174 }
  - { offset: 0x9EAF3, size: 0x8, addend: 0x0, symName: '_$s7MPhotos9PhotoCellC5frameACSo6CGRectV_tcfcTo', symObjAddr: 0x174, symBinAddr: 0x1000065D8, symSize: 0x20 }
  - { offset: 0x9EC00, size: 0x8, addend: 0x0, symName: '_$s7MPhotos9PhotoCellC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x194, symBinAddr: 0x1000065F8, symSize: 0x34 }
  - { offset: 0x9ED47, size: 0x8, addend: 0x0, symName: '_$s7MPhotos9PhotoCellC15prepareForReuseyyF', symObjAddr: 0x1C8, symBinAddr: 0x10000662C, symSize: 0x2B0 }
  - { offset: 0x9EEA7, size: 0x8, addend: 0x0, symName: '_$s7MPhotos9PhotoCellC15prepareForReuseyyFTo', symObjAddr: 0x478, symBinAddr: 0x1000068DC, symSize: 0x34 }
  - { offset: 0x9EEC8, size: 0x8, addend: 0x0, symName: '_$s7MPhotos9PhotoCellC7setupUI33_9EB19863FBF4F3179AEC9D17D32FCC60LLyyF', symObjAddr: 0x4AC, symBinAddr: 0x100006910, symSize: 0x564 }
  - { offset: 0x9EF23, size: 0x8, addend: 0x0, symName: '_$s7MPhotos9PhotoCellC16setupConstraints33_9EB19863FBF4F3179AEC9D17D32FCC60LLyyF', symObjAddr: 0xA10, symBinAddr: 0x100006E74, symSize: 0x94C }
  - { offset: 0x9EFB6, size: 0x8, addend: 0x0, symName: '_$s7MPhotos9PhotoCellC20updateSelectionState33_9EB19863FBF4F3179AEC9D17D32FCC60LLyyFyycfU_', symObjAddr: 0x135C, symBinAddr: 0x1000077C0, symSize: 0x64 }
  - { offset: 0x9EFF0, size: 0x8, addend: 0x0, symName: '_$s7MPhotos9PhotoCellC20updateSelectionState33_9EB19863FBF4F3179AEC9D17D32FCC60LLyyFySbcfU0_', symObjAddr: 0x13C0, symBinAddr: 0x100007824, symSize: 0xCC }
  - { offset: 0x9F028, size: 0x8, addend: 0x0, symName: '_$s7MPhotos9PhotoCellC9configure4with10targetSizeySo7PHAssetC_So6CGSizeVtF', symObjAddr: 0x14C8, symBinAddr: 0x1000078F0, symSize: 0x1E8 }
  - { offset: 0x9F0DC, size: 0x8, addend: 0x0, symName: '_$s7MPhotos9PhotoCellC9configure4with10targetSizeySo7PHAssetC_So6CGSizeVtFySo7UIImageCSgcfU_', symObjAddr: 0x16B0, symBinAddr: 0x100007AD8, symSize: 0x120 }
  - { offset: 0x9F158, size: 0x8, addend: 0x0, symName: '_$s7MPhotos9PhotoCellCfD', symObjAddr: 0x17D0, symBinAddr: 0x100007BF8, symSize: 0x30 }
  - { offset: 0x9F19C, size: 0x8, addend: 0x0, symName: '_$s7MPhotos9PhotoCellC14formatDuration33_9EB19863FBF4F3179AEC9D17D32FCC60LLySSSdFTf4nd_n', symObjAddr: 0x18F0, symBinAddr: 0x100007D18, symSize: 0x140 }
  - { offset: 0x9F2EA, size: 0x8, addend: 0x0, symName: '_$s7MPhotos9PhotoCellC5coderACSgSo7NSCoderC_tcfcTf4gn_n', symObjAddr: 0x1B48, symBinAddr: 0x100007F1C, symSize: 0x158 }
  - { offset: 0x9F4FE, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC11viewDidLoadyyFTo', symObjAddr: 0x0, symBinAddr: 0x10000808C, symSize: 0x5C }
  - { offset: 0x9F6EB, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerCMa', symObjAddr: 0x578, symBinAddr: 0x100008604, symSize: 0x20 }
  - { offset: 0x9F74B, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC11viewDidLoadyyFTo', symObjAddr: 0x0, symBinAddr: 0x10000808C, symSize: 0x5C }
  - { offset: 0x9F9A6, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC20setupViewControllers33_4B23E2A9500355D8539B9FC3D9E272D8LLyyF', symObjAddr: 0x5C, symBinAddr: 0x1000080E8, symSize: 0x23C }
  - { offset: 0x9FB8C, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC15setupAppearance33_4B23E2A9500355D8539B9FC3D9E272D8LLyyF', symObjAddr: 0x298, symBinAddr: 0x100008324, symSize: 0x108 }
  - { offset: 0x9FC39, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC4tabsACSaySo5UITabCG_tcfcTo', symObjAddr: 0x3A0, symBinAddr: 0x10000842C, symSize: 0x98 }
  - { offset: 0x9FC91, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC7nibName6bundleACSSSg_So8NSBundleCSgtcfcTo', symObjAddr: 0x438, symBinAddr: 0x1000084C4, symSize: 0x9C }
  - { offset: 0x9FCEB, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x4D4, symBinAddr: 0x100008560, symSize: 0x74 }
  - { offset: 0x9FD22, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerCfD', symObjAddr: 0x548, symBinAddr: 0x1000085D4, symSize: 0x30 }
  - { offset: 0x9FD67, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC09createNavE033_4B23E2A9500355D8539B9FC3D9E272D8LL6rootVC5title9imageNameSo012UINavigationE0CSo06UIViewE0C_S2StFTf4nnnd_n', symObjAddr: 0x598, symBinAddr: 0x100008624, symSize: 0x1B8 }
  - { offset: 0x9FE4F, size: 0x8, addend: 0x0, symName: '_$s7MPhotos20MainTabBarControllerC19createPlaceholderVC33_4B23E2A9500355D8539B9FC3D9E272D8LL5titleSo06UIViewE0CSS_tFTf4nd_n', symObjAddr: 0x750, symBinAddr: 0x1000087DC, symSize: 0x374 }
  - { offset: 0xA00B5, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_29didFinishLaunchingWithOptionsSbSo13UIApplicationC_SDySo0j6LaunchI3KeyaypGSgtFTo', symObjAddr: 0x0, symBinAddr: 0x100008B50, symSize: 0x8C }
  - { offset: 0xA0211, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateCMa', symObjAddr: 0x264, symBinAddr: 0x100008DB4, symSize: 0x20 }
  - { offset: 0xA0225, size: 0x8, addend: 0x0, symName: '_$sSo29UIApplicationLaunchOptionsKeyaABSHSCWl', symObjAddr: 0x33C, symBinAddr: 0x100008E8C, symSize: 0x44 }
  - { offset: 0xA0239, size: 0x8, addend: 0x0, symName: '_$sSo15UINavigationBarCMa', symObjAddr: 0x380, symBinAddr: 0x100008ED0, symSize: 0x44 }
  - { offset: 0xA036D, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_29didFinishLaunchingWithOptionsSbSo13UIApplicationC_SDySo0j6LaunchI3KeyaypGSgtFTo', symObjAddr: 0x0, symBinAddr: 0x100008B50, symSize: 0x8C }
  - { offset: 0xA03F7, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_26configurationForConnecting7optionsSo20UISceneConfigurationCSo13UIApplicationC_So0I7SessionCSo0I17ConnectionOptionsCtFTo', symObjAddr: 0x8C, symBinAddr: 0x100008BDC, symSize: 0xE0 }
  - { offset: 0xA0490, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_23didDiscardSceneSessionsySo13UIApplicationC_ShySo14UISceneSessionCGtFTo', symObjAddr: 0x16C, symBinAddr: 0x100008CBC, symSize: 0x4 }
  - { offset: 0xA04B8, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateCACycfcTo', symObjAddr: 0x170, symBinAddr: 0x100008CC0, symSize: 0x3C }
  - { offset: 0xA04ED, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateCfD', symObjAddr: 0x1AC, symBinAddr: 0x100008CFC, symSize: 0x30 }
  - { offset: 0xA0537, size: 0x8, addend: 0x0, symName: _main, symObjAddr: 0x1DC, symBinAddr: 0x100008D2C, symSize: 0x88 }
  - { offset: 0xA0596, size: 0x8, addend: 0x0, symName: '_$s7MPhotos11AppDelegateC11application_29didFinishLaunchingWithOptionsSbSo13UIApplicationC_SDySo0j6LaunchI3KeyaypGSgtFTf4ddd_n', symObjAddr: 0x284, symBinAddr: 0x100008DD4, symSize: 0xB8 }
  - { offset: 0xA06C0, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC6windowSo8UIWindowCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x100008F14, symSize: 0x10 }
  - { offset: 0xA08C6, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateCfETo', symObjAddr: 0x264, symBinAddr: 0x100009178, symSize: 0x10 }
  - { offset: 0xA08F5, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateCMa', symObjAddr: 0x274, symBinAddr: 0x100009188, symSize: 0x20 }
  - { offset: 0xA0909, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC5scene_13willConnectTo7optionsySo7UISceneC_So0I7SessionCSo0I17ConnectionOptionsCtFyyScMYccfU_TA', symObjAddr: 0x5E8, symBinAddr: 0x1000094B8, symSize: 0x8 }
  - { offset: 0xA091D, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x5F0, symBinAddr: 0x1000094C0, symSize: 0x10 }
  - { offset: 0xA0931, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x600, symBinAddr: 0x1000094D0, symSize: 0x8 }
  - { offset: 0xA0945, size: 0x8, addend: 0x0, symName: '_$s8Dispatch0A13WorkItemFlagsVACs10SetAlgebraAAWl', symObjAddr: 0x608, symBinAddr: 0x1000094D8, symSize: 0x44 }
  - { offset: 0xA0A6F, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC6windowSo8UIWindowCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x100008F14, symSize: 0x10 }
  - { offset: 0xA0A87, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC6windowSo8UIWindowCSgvgTo', symObjAddr: 0x0, symBinAddr: 0x100008F14, symSize: 0x10 }
  - { offset: 0xA0ACF, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC6windowSo8UIWindowCSgvsTo', symObjAddr: 0x10, symBinAddr: 0x100008F24, symSize: 0x34 }
  - { offset: 0xA0B2F, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC5scene_13willConnectTo7optionsySo7UISceneC_So0I7SessionCSo0I17ConnectionOptionsCtFyyScMYccfU_', symObjAddr: 0x44, symBinAddr: 0x100008F58, symSize: 0x38 }
  - { offset: 0xA0B95, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC5scene_13willConnectTo7optionsySo7UISceneC_So0I7SessionCSo0I17ConnectionOptionsCtFTo', symObjAddr: 0x7C, symBinAddr: 0x100008F90, symSize: 0x7C }
  - { offset: 0xA0BBF, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC18sceneDidDisconnectyySo7UISceneCFTo', symObjAddr: 0xF8, symBinAddr: 0x10000900C, symSize: 0x4 }
  - { offset: 0xA0BD3, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC20sceneDidBecomeActiveyySo7UISceneCFTo', symObjAddr: 0xFC, symBinAddr: 0x100009010, symSize: 0x4 }
  - { offset: 0xA0BE7, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC21sceneWillResignActiveyySo7UISceneCFTo', symObjAddr: 0x100, symBinAddr: 0x100009014, symSize: 0x4 }
  - { offset: 0xA0BFB, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC24sceneWillEnterForegroundyySo7UISceneCFTo', symObjAddr: 0x104, symBinAddr: 0x100009018, symSize: 0x4 }
  - { offset: 0xA0C0F, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC23sceneDidEnterBackgroundyySo7UISceneCFTo', symObjAddr: 0x108, symBinAddr: 0x10000901C, symSize: 0x4 }
  - { offset: 0xA0C2F, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC27checkPhotoLibraryPermission33_2145FA1267F93B6F4563514BDBB8947ELL4fromySo16UIViewControllerC_tFyAA0G7ManagerC0G6StatusOcfU_', symObjAddr: 0x10C, symBinAddr: 0x100009020, symSize: 0xE0 }
  - { offset: 0xA0CCF, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateCACycfcTo', symObjAddr: 0x1EC, symBinAddr: 0x100009100, symSize: 0x48 }
  - { offset: 0xA0D04, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateCfD', symObjAddr: 0x234, symBinAddr: 0x100009148, symSize: 0x30 }
  - { offset: 0xA0D84, size: 0x8, addend: 0x0, symName: '_$s7MPhotos13SceneDelegateC5scene_13willConnectTo7optionsySo7UISceneC_So0I7SessionCSo0I17ConnectionOptionsCtFTf4nddn_n', symObjAddr: 0x294, symBinAddr: 0x1000091A8, symSize: 0x2E4 }
  - { offset: 0xA0FAD, size: 0x8, addend: 0x0, symName: '_$sIeg_IeyB_TR', symObjAddr: 0x0, symBinAddr: 0x10000951C, symSize: 0x2C }
  - { offset: 0xA0FD1, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerC6sharedACvpZ', symObjAddr: 0x5BE8, symBinAddr: 0x10001AD78, symSize: 0x0 }
  - { offset: 0xA1118, size: 0x8, addend: 0x0, symName: '_$sIeg_IeyB_TR', symObjAddr: 0x0, symBinAddr: 0x10000951C, symSize: 0x2C }
  - { offset: 0xA1130, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerC6shared_WZ', symObjAddr: 0x2C, symBinAddr: 0x100009548, symSize: 0x54 }
  - { offset: 0xA11D1, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerCMa', symObjAddr: 0x634, symBinAddr: 0x100009B50, symSize: 0x20 }
  - { offset: 0xA11E5, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerC16requestThumbnail3for10targetSize10completions5Int32VSo7PHAssetC_So6CGSizeVySo7UIImageCSgctFyAP_SDys11AnyHashableVypGSgtcfU_yyScMYccfU_TA', symObjAddr: 0x680, symBinAddr: 0x100009B9C, symSize: 0x28 }
  - { offset: 0xA1221, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x6A8, symBinAddr: 0x100009BC4, symSize: 0x10 }
  - { offset: 0xA1235, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x6B8, symBinAddr: 0x100009BD4, symSize: 0x8 }
  - { offset: 0xA1249, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerC16requestThumbnail3for10targetSize10completions5Int32VSo7PHAssetC_So6CGSizeVySo7UIImageCSgctFyAP_SDys11AnyHashableVypGSgtcfU_TA', symObjAddr: 0x7FC, symBinAddr: 0x100009C00, symSize: 0x8 }
  - { offset: 0xA1456, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerC14fetchAllPhotosSo13PHFetchResultCySo7PHAssetCGyF', symObjAddr: 0x80, symBinAddr: 0x10000959C, symSize: 0x164 }
  - { offset: 0xA1590, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerC03getB10StatisticsAA0bF0VyF', symObjAddr: 0x1E4, symBinAddr: 0x100009700, symSize: 0xF4 }
  - { offset: 0xA16C8, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerC16requestThumbnail3for10targetSize10completions5Int32VSo7PHAssetC_So6CGSizeVySo7UIImageCSgctF', symObjAddr: 0x2D8, symBinAddr: 0x1000097F4, symSize: 0x15C }
  - { offset: 0xA175E, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerC16requestThumbnail3for10targetSize10completions5Int32VSo7PHAssetC_So6CGSizeVySo7UIImageCSgctFyAP_SDys11AnyHashableVypGSgtcfU_', symObjAddr: 0x434, symBinAddr: 0x100009950, symSize: 0x1D4 }
  - { offset: 0xA1850, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerCfD', symObjAddr: 0x608, symBinAddr: 0x100009B24, symSize: 0x2C }
  - { offset: 0xA18AB, size: 0x8, addend: 0x0, symName: '_$s7MPhotos16PhotoDataManagerC11fetchAssets4withSo13PHFetchResultCySo7PHAssetCGSo0J9MediaTypeV_tFTf4nd_n', symObjAddr: 0x804, symBinAddr: 0x100009C08, symSize: 0x158 }
  - { offset: 0xA1A9D, size: 0x8, addend: 0x0, symName: '_$sSo7UIImageCSgSDys11AnyHashableVypGSgIeggg_ACSo12NSDictionaryCSgIeyByy_TR', symObjAddr: 0x2C, symBinAddr: 0x100009F54, symSize: 0x94 }
  - { offset: 0xA1CE6, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerCfETo', symObjAddr: 0x22AC, symBinAddr: 0x10000C1D4, symSize: 0x98 }
  - { offset: 0xA1D15, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerCMa', symObjAddr: 0x2344, symBinAddr: 0x10000C26C, symSize: 0x20 }
  - { offset: 0xA1D29, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC04testB7Loading33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyFySo7UIImageCSgcfU_TA', symObjAddr: 0x23C4, symBinAddr: 0x10000C2EC, symSize: 0xC }
  - { offset: 0xA1D3D, size: 0x8, addend: 0x0, symName: _block_copy_helper, symObjAddr: 0x23FC, symBinAddr: 0x10000C31C, symSize: 0x10 }
  - { offset: 0xA1D51, size: 0x8, addend: 0x0, symName: _block_destroy_helper, symObjAddr: 0x240C, symBinAddr: 0x10000C32C, symSize: 0x8 }
  - { offset: 0xA1D65, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC04loadB4Data33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyFyyYbcfU_TA', symObjAddr: 0x2414, symBinAddr: 0x10000C334, symSize: 0x8 }
  - { offset: 0xA1D79, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC04loadB4Data33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyFyyYbcfU_yyScMYccfU_TA', symObjAddr: 0x2558, symBinAddr: 0x10000C360, symSize: 0xC }
  - { offset: 0xA1DAD, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC11viewDidLoadyyFTo', symObjAddr: 0xC0, symBinAddr: 0x100009FE8, symSize: 0x60 }
  - { offset: 0xA1EEC, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC7setupUI33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyF', symObjAddr: 0x120, symBinAddr: 0x10000A048, symSize: 0x68C }
  - { offset: 0xA1F6D, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC16setupConstraints33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyF', symObjAddr: 0x7AC, symBinAddr: 0x10000A6D4, symSize: 0x6E0 }
  - { offset: 0xA20B1, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC26checkPermissionAndLoadData33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyF', symObjAddr: 0xE8C, symBinAddr: 0x10000ADB4, symSize: 0x198 }
  - { offset: 0xA218B, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC04loadB4Data33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyF', symObjAddr: 0x1024, symBinAddr: 0x10000AF4C, symSize: 0x274 }
  - { offset: 0xA21EE, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC04loadB4Data33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyFyyYbcfU_', symObjAddr: 0x1298, symBinAddr: 0x10000B1C0, symSize: 0x274 }
  - { offset: 0xA2289, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC04loadB4Data33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyFyyYbcfU_yyScMYccfU_', symObjAddr: 0x150C, symBinAddr: 0x10000B434, symSize: 0x58 }
  - { offset: 0xA22FB, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC8updateUI33_1F57B767E7DC0F6E44807B7E3A1ABA97LL4withyAA0B10StatisticsV_tF', symObjAddr: 0x1564, symBinAddr: 0x10000B48C, symSize: 0x2B4 }
  - { offset: 0xA25FB, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC04testB7Loading33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyF', symObjAddr: 0x1818, symBinAddr: 0x10000B740, symSize: 0x48C }
  - { offset: 0xA27E3, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC04testB7Loading33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyFySo7UIImageCSgcfU_', symObjAddr: 0x1CA4, symBinAddr: 0x10000BBCC, symSize: 0x1BC }
  - { offset: 0xA28D1, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC04testB7Loading33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyFTo', symObjAddr: 0x1E60, symBinAddr: 0x10000BD88, symSize: 0x34 }
  - { offset: 0xA28F9, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC11refreshData33_1F57B767E7DC0F6E44807B7E3A1ABA97LLyyFTo', symObjAddr: 0x1E94, symBinAddr: 0x10000BDBC, symSize: 0x34 }
  - { offset: 0xA2974, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC7nibName6bundleACSSSg_So8NSBundleCSgtcfc', symObjAddr: 0x1EC8, symBinAddr: 0x10000BDF0, symSize: 0x1B0 }
  - { offset: 0xA2AAE, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC7nibName6bundleACSSSg_So8NSBundleCSgtcfcTo', symObjAddr: 0x2078, symBinAddr: 0x10000BFA0, symSize: 0x5C }
  - { offset: 0xA2ADA, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC5coderACSgSo7NSCoderC_tcfc', symObjAddr: 0x20D4, symBinAddr: 0x10000BFFC, symSize: 0x184 }
  - { offset: 0xA2C06, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerC5coderACSgSo7NSCoderC_tcfcTo', symObjAddr: 0x2258, symBinAddr: 0x10000C180, symSize: 0x24 }
  - { offset: 0xA2C1A, size: 0x8, addend: 0x0, symName: '_$s7MPhotos27PhotoGridTestViewControllerCfD', symObjAddr: 0x227C, symBinAddr: 0x10000C1A4, symSize: 0x30 }
  - { offset: 0xA2D65, size: 0x8, addend: 0x0, symName: '_$s7MPhotos19ResourceBundleClass33_E00857F5A182E2036586C1283CBC8150LLCMa', symObjAddr: 0x10, symBinAddr: 0x10000C37C, symSize: 0x20 }
...
