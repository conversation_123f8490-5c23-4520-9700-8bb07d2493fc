{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "DEVELOPMENT_TEAM": "5BNRB84BM8", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.5", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "9765ef73a2a045bb016acc8828e954d139af8169a0bd7bdfd60870199cdfe519", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEVELOPMENT_TEAM": "5BNRB84BM8", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.5", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "9765ef73a2a045bb016acc8828e954d12775ececcb9c4afaec6016ce1ea24444", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1e461759031710a2b00e789a7c511d649", "path": "MainTabBarController.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1b4d4507b9b271028cfccd17cbabfcc73", "name": "App", "path": "App", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d1d21f1369f80d609e8df900e16863a7dc", "name": "Core", "path": "Core", "sourceTree": "<group>", "type": "group"}, {"children": [{"guid": "9765ef73a2a045bb016acc8828e954d196e7ae38d989fe0a5cc0b814103ff17a", "name": "Albums", "path": "Albums", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d14c87d7c710183ea98e93866d72fb02b9", "name": "Cleanup", "path": "Cleanup", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d18469b9ec603d25682d29368eb094c90e", "name": "PhotoGrid", "path": "PhotoGrid", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d1862e5757853283cb3d8afc54f7377e6b", "name": "Tools", "path": "Tools", "sourceTree": "<group>", "type": "group"}], "guid": "9765ef73a2a045bb016acc8828e954d1314ef416ee8b5959b2ee4e475fdf5b83", "name": "Features", "path": "Features", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d102675cebb58ace7a9dedd8e61a81dc08", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d1e8dbaebe17a43c3b49c9394588401b29", "name": "SupportingFiles", "path": "SupportingFiles", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d1dfd1a3c07aee9e31b1d81ce406b39184", "name": "Utilities", "path": "Utilities", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d10e9ed47748e1f8453faeeac2e4eb9665", "path": "AppDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "9765ef73a2a045bb016acc8828e954d1ed0fd246344a2f3c16fbb1f8674e079b", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "9765ef73a2a045bb016acc8828e954d14245ceb55297a14485dcd9f4585361cf", "path": "Info.plist", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "file.storyboard", "guid": "9765ef73a2a045bb016acc8828e954d1cdc1713d595a16879e5ece012a5c2d9a", "path": "Base.lproj/LaunchScreen.storyboard", "regionVariantName": "Base", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1a0685d2e7ed1b4557c080f10975775c0", "name": "LaunchScreen.storyboard", "path": "", "sourceTree": "<group>", "type": "variantGroup"}, {"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1c4db2fe9281d180d0fcacfe1f18faeac", "path": "SceneDelegate.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1762a9d5cfa01083c5f0a14088ea1ad3e", "name": "MPhotos", "path": "MPhotos", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d10a96e24c51547c907c57c2a983dd9f47", "path": "MPhotosTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d16fb46d7ed9de4ef70b202a1490ce36ac", "name": "MPhotosTests", "path": "MPhotosTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1f147f7f9de27e14a0550b2633b080487", "path": "MPhotosUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d18a510604dba42bedb17d9a9cc3a728a8", "path": "MPhotosUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1155f058d0736826f31b74761febe3e7c", "name": "MPhotosUITests", "path": "MPhotosUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d1853c28b37f2c93ea4dbc40bfd83bd325", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "9765ef73a2a045bb016acc8828e954d1a40d15467834ba4aac9156533c785ad5", "name": "MPhotos", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "9765ef73a2a045bb016acc8828e954d1", "path": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos", "targets": ["TARGET@v11_hash=9851378158ac9bb0cfade9d390641b20", "TARGET@v11_hash=ea9cc1fe923dee6ecf3602ab21cdda05", "TARGET@v11_hash=067a086c13114aaf916cefab114b0cde"]}