{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "DEVELOPMENT_TEAM": "5BNRB84BM8", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.5", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "9765ef73a2a045bb016acc8828e954d139af8169a0bd7bdfd60870199cdfe519", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEVELOPMENT_TEAM": "5BNRB84BM8", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.5", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "9765ef73a2a045bb016acc8828e954d12775ececcb9c4afaec6016ce1ea24444", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d18ff1a74dedd1c014084305d4613041b4", "path": "MainTabBarController.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1847568f1a29497f5ba86842dd61bb29e", "name": "App", "path": "App", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1c25a97c4922995ddb365e32e6d1d663b", "path": "PhotoDataManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1f098ed2c7e842ecb8b5dcaffef310394", "name": "Core", "path": "Core", "sourceTree": "<group>", "type": "group"}, {"children": [{"guid": "9765ef73a2a045bb016acc8828e954d1d0074098f3c9cc68b417256bf532bcc9", "name": "Albums", "path": "Albums", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d1421bb01475df5a7ba8730a764974ba1a", "name": "Cleanup", "path": "Cleanup", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d19fc9552ac9133d6ba383cc28179bcfbd", "path": "PhotoCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1ca72ebae6d88975edd8d9e0178c30a9f", "path": "PhotoGridTestViewController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1f240f2a8b981d167a69bd13407d451b9", "path": "PhotoGridViewController.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d14a54c3d027efe2c7e3a66b24975dc63c", "name": "PhotoGrid", "path": "PhotoGrid", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d13bd388abbcfd46de8f5db7f3d66bb5ab", "name": "Tools", "path": "Tools", "sourceTree": "<group>", "type": "group"}], "guid": "9765ef73a2a045bb016acc8828e954d19b0d17395e8c210560bd3bf961c4fb7a", "name": "Features", "path": "Features", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d1cb3216862d2c5ba3255c7dba28bb7b82", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d1ed2c85985a4558aab36f7bb8e43575aa", "name": "SupportingFiles", "path": "SupportingFiles", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1c2bcdecaa10c80c388280855785cf224", "path": "PermissionManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1c0d6b1d9fa61039b9b1ccc324edab702", "name": "Utilities", "path": "Utilities", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d155fa8f08b22bb0f7b69d08a268c3b51e", "path": "AppDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "9765ef73a2a045bb016acc8828e954d1f95fca510514634593c958f1bfd4d113", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "9765ef73a2a045bb016acc8828e954d1c8bd7a8846a7f50bc355b32d648ebafe", "path": "Info.plist", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "file.storyboard", "guid": "9765ef73a2a045bb016acc8828e954d14564e4f3690c0f64688956d0aae4400e", "path": "Base.lproj/LaunchScreen.storyboard", "regionVariantName": "Base", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1026d301bc609c71a9ea140add3e59ea7", "name": "LaunchScreen.storyboard", "path": "", "sourceTree": "<group>", "type": "variantGroup"}, {"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d112d6c9bc91201a57a8e63fdf8e561ea7", "path": "SceneDelegate.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1762a9d5cfa01083c5f0a14088ea1ad3e", "name": "MPhotos", "path": "MPhotos", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1327a02ea138b4d626cf8097319b38bd8", "path": "MPhotosTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d16fb46d7ed9de4ef70b202a1490ce36ac", "name": "MPhotosTests", "path": "MPhotosTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1c310a5937e9344ca7c19aca547004811", "path": "MPhotosUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d19071f038269432b21a3fdfbf11fab2a8", "path": "MPhotosUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1155f058d0736826f31b74761febe3e7c", "name": "MPhotosUITests", "path": "MPhotosUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d1853c28b37f2c93ea4dbc40bfd83bd325", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "9765ef73a2a045bb016acc8828e954d1a40d15467834ba4aac9156533c785ad5", "name": "MPhotos", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "9765ef73a2a045bb016acc8828e954d1", "path": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos", "targets": ["TARGET@v11_hash=47e621b93e1ab50ecb10760b78133156", "TARGET@v11_hash=9b49d2065152e25faf944a1e345b59e8", "TARGET@v11_hash=e863870a95b450f458eba9bc38870613"]}