{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "DEVELOPMENT_TEAM": "5BNRB84BM8", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.5", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "9765ef73a2a045bb016acc8828e954d139af8169a0bd7bdfd60870199cdfe519", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEVELOPMENT_TEAM": "5BNRB84BM8", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.5", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "9765ef73a2a045bb016acc8828e954d12775ececcb9c4afaec6016ce1ea24444", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1930ab5523073a20157110870ded9b185", "path": "MainTabBarController.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1d4dda3705eb855a81e00f7005a319277", "name": "App", "path": "App", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d1a6805014086f39415c7841f32bccd13b", "name": "Core", "path": "Core", "sourceTree": "<group>", "type": "group"}, {"children": [{"guid": "9765ef73a2a045bb016acc8828e954d1809ed887d5e5ca9016d84eb3e20d3e17", "name": "Albums", "path": "Albums", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d127d42996f4f210c48cc5459002ece174", "name": "Cleanup", "path": "Cleanup", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d1de2da1f24cbb572fb30091ca8fbf1e7d", "name": "PhotoGrid", "path": "PhotoGrid", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d149f46eb90c1ebcd907be186f4bf81bb6", "name": "Tools", "path": "Tools", "sourceTree": "<group>", "type": "group"}], "guid": "9765ef73a2a045bb016acc8828e954d1ffaabdc6a6ff36874e8b31f0616ba2bf", "name": "Features", "path": "Features", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d1eb9723374f0e0e675412d20c0e808677", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d190efebfec79bf927bf0dc2f05eaeb9cc", "name": "SupportingFiles", "path": "SupportingFiles", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d197d486b3dc51e69cc71648c4c81af135", "path": "PermissionManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1393c9a766a26db0df654fae4f5833ebf", "name": "Utilities", "path": "Utilities", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1a986c3100c27fc4a371c49a71b67e2e8", "path": "AppDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "9765ef73a2a045bb016acc8828e954d103ce0f47e98191bdf33bcdb7ac0a6c86", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "9765ef73a2a045bb016acc8828e954d1fb4a67be8ed76559683e110545f4984a", "path": "Info.plist", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "file.storyboard", "guid": "9765ef73a2a045bb016acc8828e954d1f0b38ce4841038abcb02be39b81370ea", "path": "Base.lproj/LaunchScreen.storyboard", "regionVariantName": "Base", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d129f09283f1fb920a630cb1bf35cf0e03", "name": "LaunchScreen.storyboard", "path": "", "sourceTree": "<group>", "type": "variantGroup"}, {"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1a770b88fd3f13080fd2fe64622dd8e32", "path": "SceneDelegate.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1762a9d5cfa01083c5f0a14088ea1ad3e", "name": "MPhotos", "path": "MPhotos", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1b31996d5c91427e33283e027f6fe80ac", "path": "MPhotosTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d16fb46d7ed9de4ef70b202a1490ce36ac", "name": "MPhotosTests", "path": "MPhotosTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d163767e571ce3ad8bf5a251e0d048c429", "path": "MPhotosUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1b46955b553102544dc9a3d15e4670052", "path": "MPhotosUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1155f058d0736826f31b74761febe3e7c", "name": "MPhotosUITests", "path": "MPhotosUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d1853c28b37f2c93ea4dbc40bfd83bd325", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "9765ef73a2a045bb016acc8828e954d1a40d15467834ba4aac9156533c785ad5", "name": "MPhotos", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "9765ef73a2a045bb016acc8828e954d1", "path": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos", "targets": ["TARGET@v11_hash=b5656ab1acfd551bbc9f82c789c5a5eb", "TARGET@v11_hash=471e6c93e267352fd5792083bd9e3dc0", "TARGET@v11_hash=f68be36a9f3678ef29ef8feb36dd4380"]}