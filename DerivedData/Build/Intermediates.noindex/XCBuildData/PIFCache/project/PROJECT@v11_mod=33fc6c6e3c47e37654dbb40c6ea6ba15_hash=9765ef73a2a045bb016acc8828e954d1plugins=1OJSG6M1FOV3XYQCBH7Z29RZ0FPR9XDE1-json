{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "DEVELOPMENT_TEAM": "5BNRB84BM8", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.5", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "9765ef73a2a045bb016acc8828e954d139af8169a0bd7bdfd60870199cdfe519", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEVELOPMENT_TEAM": "5BNRB84BM8", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.5", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "9765ef73a2a045bb016acc8828e954d12775ececcb9c4afaec6016ce1ea24444", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d103b85da9c1fe666cbaba468ba78c6e4d", "path": "MainTabBarController.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1a9bb9b57eb9a43cdf113e5a26b3415ce", "name": "App", "path": "App", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d14c994766015a2cfb5c4aba4438aad6c8", "path": "PhotoDataManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d18073e3b8168edcbcb214a10ffd14612c", "name": "Core", "path": "Core", "sourceTree": "<group>", "type": "group"}, {"children": [{"guid": "9765ef73a2a045bb016acc8828e954d1a7f7a5641f563968d1908347c5974f5c", "name": "Albums", "path": "Albums", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d11c82145de0fcdef0ad1b4d212f0f457e", "name": "Cleanup", "path": "Cleanup", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d103b03789386fc166fd14c1cdb1fc8c30", "path": "PhotoGridTestViewController.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1d6237cbd392b5b30d0032994109945d1", "name": "PhotoGrid", "path": "PhotoGrid", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d1a0c5257511e0536192a6e71b4d363902", "name": "Tools", "path": "Tools", "sourceTree": "<group>", "type": "group"}], "guid": "9765ef73a2a045bb016acc8828e954d117fb27c97ef630c3c8328e49575389c1", "name": "Features", "path": "Features", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d19e30c06815550bef5630bc72d871dd8f", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d1f698f8b5493ebf49a7f8e80fcf2e920d", "name": "SupportingFiles", "path": "SupportingFiles", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1e8a7acb0d10d1359f92b2d7f30edfb09", "path": "PermissionManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1739965ae02491d1298adee2477e3f5e1", "name": "Utilities", "path": "Utilities", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d117cc244a159095f0f1382ff5a1516b9e", "path": "AppDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "9765ef73a2a045bb016acc8828e954d1616bf4dd72f3e26fc74bcb97e75b9fed", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "9765ef73a2a045bb016acc8828e954d1e06a686214110bdb426c93423c332930", "path": "Info.plist", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "file.storyboard", "guid": "9765ef73a2a045bb016acc8828e954d104b363053cc408f46b99a7e786bfbece", "path": "Base.lproj/LaunchScreen.storyboard", "regionVariantName": "Base", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1a53f534cc0863cbf457ff70373985151", "name": "LaunchScreen.storyboard", "path": "", "sourceTree": "<group>", "type": "variantGroup"}, {"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1902b23045378ad651b206a262c59f422", "path": "SceneDelegate.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1762a9d5cfa01083c5f0a14088ea1ad3e", "name": "MPhotos", "path": "MPhotos", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1329e940b551c59ae087edd70471dc894", "path": "MPhotosTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d16fb46d7ed9de4ef70b202a1490ce36ac", "name": "MPhotosTests", "path": "MPhotosTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1e7bb7b849365f1dff98644084fafdc91", "path": "MPhotosUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1b31c02cf2ff402caac562633662fbd34", "path": "MPhotosUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1155f058d0736826f31b74761febe3e7c", "name": "MPhotosUITests", "path": "MPhotosUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d1853c28b37f2c93ea4dbc40bfd83bd325", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "9765ef73a2a045bb016acc8828e954d1a40d15467834ba4aac9156533c785ad5", "name": "MPhotos", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "9765ef73a2a045bb016acc8828e954d1", "path": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos", "targets": ["TARGET@v11_hash=e268f94523c0c5a50c15ff1322ff157c", "TARGET@v11_hash=d122c5f327363a8cbace07d551a3737a", "TARGET@v11_hash=d8f676c782108642dce628bfb76d35d6"]}