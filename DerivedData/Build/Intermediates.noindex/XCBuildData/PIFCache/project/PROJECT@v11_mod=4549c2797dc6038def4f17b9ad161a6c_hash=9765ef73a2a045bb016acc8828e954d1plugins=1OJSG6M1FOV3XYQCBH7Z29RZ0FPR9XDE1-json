{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "DEVELOPMENT_TEAM": "5BNRB84BM8", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.5", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "9765ef73a2a045bb016acc8828e954d139af8169a0bd7bdfd60870199cdfe519", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEVELOPMENT_TEAM": "5BNRB84BM8", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.5", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "9765ef73a2a045bb016acc8828e954d12775ececcb9c4afaec6016ce1ea24444", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1c83928ebb7bf8161487b055bbe2eec57", "path": "MainTabBarController.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1abc59dad4789f931c9fae7e10674f48c", "name": "App", "path": "App", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d13493c05b88e1650b13f701bc2f3c9ddd", "name": "Core", "path": "Core", "sourceTree": "<group>", "type": "group"}, {"children": [{"guid": "9765ef73a2a045bb016acc8828e954d138f1d40b6c99a1ae2610752d84cde160", "name": "Albums", "path": "Albums", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d105375bcbf9c66e5f108b43e0bb544967", "name": "Cleanup", "path": "Cleanup", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d10639081dcd35771cbd5d303f17f93171", "name": "PhotoGrid", "path": "PhotoGrid", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d11197df62235e6bcb81f81c7a657bc150", "name": "Tools", "path": "Tools", "sourceTree": "<group>", "type": "group"}], "guid": "9765ef73a2a045bb016acc8828e954d1548413f75412cf3b199ba5a7a5ef05d7", "name": "Features", "path": "Features", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d10f014b974bf4ccb2b6f4649b8a15facd", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d15330e4880850b3a4968c9f2936a8f6ea", "name": "SupportingFiles", "path": "SupportingFiles", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d1551d71ece2745bad60eafc2dd0c0862d", "name": "Utilities", "path": "Utilities", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1d93ee255385e5eeba851480ab7637d1f", "path": "AppDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "9765ef73a2a045bb016acc8828e954d1443a246b37801bdd9f4c465cd0fa59c8", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "9765ef73a2a045bb016acc8828e954d1c4d8be6714667f49437e51f73666ec42", "path": "Info.plist", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "file.storyboard", "guid": "9765ef73a2a045bb016acc8828e954d113d1a596fc416f5671569807b4245551", "path": "Base.lproj/LaunchScreen.storyboard", "regionVariantName": "Base", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1183d8f0086cad77f1a2be65fc6f50bd5", "name": "LaunchScreen.storyboard", "path": "", "sourceTree": "<group>", "type": "variantGroup"}, {"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d116dbf1fa7fd7e657ccb0e8da73e6b52b", "path": "SceneDelegate.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1762a9d5cfa01083c5f0a14088ea1ad3e", "name": "MPhotos", "path": "MPhotos", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d10da29c376b0064e29a562b4da3e38244", "path": "MPhotosTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d16fb46d7ed9de4ef70b202a1490ce36ac", "name": "MPhotosTests", "path": "MPhotosTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d16c285e742bf0eb59782c912e1b49cc4f", "path": "MPhotosUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "9765ef73a2a045bb016acc8828e954d1151ae2400f07cad5ee096454fcbce8ad", "path": "MPhotosUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "9765ef73a2a045bb016acc8828e954d1155f058d0736826f31b74761febe3e7c", "name": "MPhotosUITests", "path": "MPhotosUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "9765ef73a2a045bb016acc8828e954d1853c28b37f2c93ea4dbc40bfd83bd325", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "9765ef73a2a045bb016acc8828e954d1a40d15467834ba4aac9156533c785ad5", "name": "MPhotos", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "9765ef73a2a045bb016acc8828e954d1", "path": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos/MPhotos.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/纯文档驱动版/MPhotos", "targets": ["TARGET@v11_hash=a824711c9c79053b68f13ce7c95fdbb7", "TARGET@v11_hash=d272c5b13777ae627a31e180700b0321", "TARGET@v11_hash=82e2928ce4239e12c5d2cf5846845011"]}