{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "5BNRB84BM8", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_FILE": "MPhotos/Info.plist", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchStoryboardName": "LaunchScreen", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.MPCraft.MPhotos", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "9765ef73a2a045bb016acc8828e954d1ae78ede2791466cd6cf05d7e414a43a6", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "5BNRB84BM8", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_FILE": "MPhotos/Info.plist", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchStoryboardName": "LaunchScreen", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.MPCraft.MPhotos", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "9765ef73a2a045bb016acc8828e954d13fdbfe2c889176d44ea6f01b48140965", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "9765ef73a2a045bb016acc8828e954d117cc244a159095f0f1382ff5a1516b9e", "guid": "9765ef73a2a045bb016acc8828e954d163bac4314eada96f0c2317c33e9a1b46"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d14c994766015a2cfb5c4aba4438aad6c8", "guid": "9765ef73a2a045bb016acc8828e954d1c230a2e42aef59c3c55b1ed48a60d1ed"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d103b85da9c1fe666cbaba468ba78c6e4d", "guid": "9765ef73a2a045bb016acc8828e954d1d7dc0bcaf0815b5605a18a6ca3962289"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d1902b23045378ad651b206a262c59f422", "guid": "9765ef73a2a045bb016acc8828e954d1f4208c10d5f1a5ee73fd687abbf4d86c"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d1e8a7acb0d10d1359f92b2d7f30edfb09", "guid": "9765ef73a2a045bb016acc8828e954d104e5ee52db180ffe3ccea5f62fabcc8d"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d103b03789386fc166fd14c1cdb1fc8c30", "guid": "9765ef73a2a045bb016acc8828e954d1fb8ffe42e9fcf0ec7a8444f106c77fde"}], "guid": "9765ef73a2a045bb016acc8828e954d1d5acfb98d2901e9ea382aa2026c966a1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "9765ef73a2a045bb016acc8828e954d1e107f8b855791f339f8c8a61e755db1e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "9765ef73a2a045bb016acc8828e954d1616bf4dd72f3e26fc74bcb97e75b9fed", "guid": "9765ef73a2a045bb016acc8828e954d18d2ebc22678a36aec5f13f38ff891f1e"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d1a53f534cc0863cbf457ff70373985151", "guid": "9765ef73a2a045bb016acc8828e954d12643ef73166631fdc06dc65c636dfcc5"}], "guid": "9765ef73a2a045bb016acc8828e954d1f14eedc5251924716035834dc4025efd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "9765ef73a2a045bb016acc8828e954d192fc5de22e9796de56986e921c13fc8d", "name": "MPhotos", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "9765ef73a2a045bb016acc8828e954d1bb5b5d55872da6012f23b693adfe3f44", "name": "MPhotos.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}