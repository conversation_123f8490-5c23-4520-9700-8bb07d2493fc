{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "5BNRB84BM8", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_FILE": "MPhotos/Info.plist", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchStoryboardName": "LaunchScreen", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.MPCraft.MPhotos", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "9765ef73a2a045bb016acc8828e954d1ae78ede2791466cd6cf05d7e414a43a6", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "5BNRB84BM8", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_FILE": "MPhotos/Info.plist", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchStoryboardName": "LaunchScreen", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.MPCraft.MPhotos", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "9765ef73a2a045bb016acc8828e954d13fdbfe2c889176d44ea6f01b48140965", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "9765ef73a2a045bb016acc8828e954d1c4db2fe9281d180d0fcacfe1f18faeac", "guid": "9765ef73a2a045bb016acc8828e954d12b22aebd331314a179cfc12ea29f3886"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d1e461759031710a2b00e789a7c511d649", "guid": "9765ef73a2a045bb016acc8828e954d1b870d3634fefc2f9de3bf06ebfb366a5"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d10e9ed47748e1f8453faeeac2e4eb9665", "guid": "9765ef73a2a045bb016acc8828e954d18822b9f74f6a04b4b6fac924e3b38ac1"}], "guid": "9765ef73a2a045bb016acc8828e954d1d5acfb98d2901e9ea382aa2026c966a1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "9765ef73a2a045bb016acc8828e954d1e107f8b855791f339f8c8a61e755db1e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "9765ef73a2a045bb016acc8828e954d1ed0fd246344a2f3c16fbb1f8674e079b", "guid": "9765ef73a2a045bb016acc8828e954d16390ad86255fa5ea503f1f87d791d4ac"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d1a0685d2e7ed1b4557c080f10975775c0", "guid": "9765ef73a2a045bb016acc8828e954d14c2198ef1477e24f08c08443f517d3fb"}], "guid": "9765ef73a2a045bb016acc8828e954d1f14eedc5251924716035834dc4025efd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "9765ef73a2a045bb016acc8828e954d192fc5de22e9796de56986e921c13fc8d", "name": "MPhotos", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "9765ef73a2a045bb016acc8828e954d1bb5b5d55872da6012f23b693adfe3f44", "name": "MPhotos.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}