{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "5BNRB84BM8", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_FILE": "MPhotos/Info.plist", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchStoryboardName": "LaunchScreen", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.MPCraft.MPhotos", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "9765ef73a2a045bb016acc8828e954d1ae78ede2791466cd6cf05d7e414a43a6", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "5BNRB84BM8", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_FILE": "MPhotos/Info.plist", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchStoryboardName": "LaunchScreen", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.MPCraft.MPhotos", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "9765ef73a2a045bb016acc8828e954d13fdbfe2c889176d44ea6f01b48140965", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "9765ef73a2a045bb016acc8828e954d1a770b88fd3f13080fd2fe64622dd8e32", "guid": "9765ef73a2a045bb016acc8828e954d19f04c41c3c8fc404a241f03cce3aab17"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d1930ab5523073a20157110870ded9b185", "guid": "9765ef73a2a045bb016acc8828e954d1d61ecebbb4a99c7cebe9384c5ace543d"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d197d486b3dc51e69cc71648c4c81af135", "guid": "9765ef73a2a045bb016acc8828e954d11e58eb7f7dee2aaf73be62cab3416f4c"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d1a986c3100c27fc4a371c49a71b67e2e8", "guid": "9765ef73a2a045bb016acc8828e954d1d4c9bb6fda086279e5075904a00a0aa5"}], "guid": "9765ef73a2a045bb016acc8828e954d1d5acfb98d2901e9ea382aa2026c966a1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "9765ef73a2a045bb016acc8828e954d1e107f8b855791f339f8c8a61e755db1e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "9765ef73a2a045bb016acc8828e954d103ce0f47e98191bdf33bcdb7ac0a6c86", "guid": "9765ef73a2a045bb016acc8828e954d1910c93450bc76a7f891f6be92149af38"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d129f09283f1fb920a630cb1bf35cf0e03", "guid": "9765ef73a2a045bb016acc8828e954d1e123d182d3989064db575c6790ac40d6"}], "guid": "9765ef73a2a045bb016acc8828e954d1f14eedc5251924716035834dc4025efd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "9765ef73a2a045bb016acc8828e954d192fc5de22e9796de56986e921c13fc8d", "name": "MPhotos", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "9765ef73a2a045bb016acc8828e954d1bb5b5d55872da6012f23b693adfe3f44", "name": "MPhotos.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}