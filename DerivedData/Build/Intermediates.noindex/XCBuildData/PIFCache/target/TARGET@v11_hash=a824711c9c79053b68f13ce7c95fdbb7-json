{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "5BNRB84BM8", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_FILE": "MPhotos/Info.plist", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchStoryboardName": "LaunchScreen", "INFOPLIST_KEY_UIMainStoryboardFile": "Main", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.MPCraft.MPhotos", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "9765ef73a2a045bb016acc8828e954d1ae78ede2791466cd6cf05d7e414a43a6", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "5BNRB84BM8", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_FILE": "MPhotos/Info.plist", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchStoryboardName": "LaunchScreen", "INFOPLIST_KEY_UIMainStoryboardFile": "Main", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.MPCraft.MPhotos", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "9765ef73a2a045bb016acc8828e954d13fdbfe2c889176d44ea6f01b48140965", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "9765ef73a2a045bb016acc8828e954d1d93ee255385e5eeba851480ab7637d1f", "guid": "9765ef73a2a045bb016acc8828e954d1d246fbc7d6db18e5bcd108c6ce796c62"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d1c83928ebb7bf8161487b055bbe2eec57", "guid": "9765ef73a2a045bb016acc8828e954d1e4acafc8ed55a376fa54585efba05b2c"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d116dbf1fa7fd7e657ccb0e8da73e6b52b", "guid": "9765ef73a2a045bb016acc8828e954d1e7edbadb9a6bebf2ec9db81a02c6de4c"}], "guid": "9765ef73a2a045bb016acc8828e954d1d5acfb98d2901e9ea382aa2026c966a1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "9765ef73a2a045bb016acc8828e954d1e107f8b855791f339f8c8a61e755db1e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "9765ef73a2a045bb016acc8828e954d1443a246b37801bdd9f4c465cd0fa59c8", "guid": "9765ef73a2a045bb016acc8828e954d1339d5e24f56f8c4feea4057787fc9c17"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d1183d8f0086cad77f1a2be65fc6f50bd5", "guid": "9765ef73a2a045bb016acc8828e954d1327077b38742f1e92f6724dd23e552ce"}], "guid": "9765ef73a2a045bb016acc8828e954d1f14eedc5251924716035834dc4025efd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "9765ef73a2a045bb016acc8828e954d192fc5de22e9796de56986e921c13fc8d", "name": "MPhotos", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "9765ef73a2a045bb016acc8828e954d1bb5b5d55872da6012f23b693adfe3f44", "name": "MPhotos.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}