{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "5BNRB84BM8", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_FILE": "MPhotos/Info.plist", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchStoryboardName": "LaunchScreen", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.MPCraft.MPhotos", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "9765ef73a2a045bb016acc8828e954d1ae78ede2791466cd6cf05d7e414a43a6", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "5BNRB84BM8", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_FILE": "MPhotos/Info.plist", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchStoryboardName": "LaunchScreen", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.MPCraft.MPhotos", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "9765ef73a2a045bb016acc8828e954d13fdbfe2c889176d44ea6f01b48140965", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "9765ef73a2a045bb016acc8828e954d1f240f2a8b981d167a69bd13407d451b9", "guid": "9765ef73a2a045bb016acc8828e954d15500c0c1e7e94e06c915b86feb33a66a"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d1c2bcdecaa10c80c388280855785cf224", "guid": "9765ef73a2a045bb016acc8828e954d104a27a22e6dc5c883a777ed588c334f3"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d19fc9552ac9133d6ba383cc28179bcfbd", "guid": "9765ef73a2a045bb016acc8828e954d1cbf04885231487a442150dfe98d2e6c1"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d18ff1a74dedd1c014084305d4613041b4", "guid": "9765ef73a2a045bb016acc8828e954d1cdfe246cc86ccd611ec837a04cd62880"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d155fa8f08b22bb0f7b69d08a268c3b51e", "guid": "9765ef73a2a045bb016acc8828e954d14eec1a54e04c043bc6b674065a8b0690"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d112d6c9bc91201a57a8e63fdf8e561ea7", "guid": "9765ef73a2a045bb016acc8828e954d114af786a975a68431879953cc181aed1"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d1c25a97c4922995ddb365e32e6d1d663b", "guid": "9765ef73a2a045bb016acc8828e954d1e3930ce7662c7c1649f3ddf083a889b3"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d1ca72ebae6d88975edd8d9e0178c30a9f", "guid": "9765ef73a2a045bb016acc8828e954d151bf9203e8e22f5f1a330e3ae6d5b387"}], "guid": "9765ef73a2a045bb016acc8828e954d1d5acfb98d2901e9ea382aa2026c966a1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "9765ef73a2a045bb016acc8828e954d1e107f8b855791f339f8c8a61e755db1e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "9765ef73a2a045bb016acc8828e954d1026d301bc609c71a9ea140add3e59ea7", "guid": "9765ef73a2a045bb016acc8828e954d1a709585c996cf7a965a8f29eb6eea7d3"}, {"fileReference": "9765ef73a2a045bb016acc8828e954d1f95fca510514634593c958f1bfd4d113", "guid": "9765ef73a2a045bb016acc8828e954d16b76a775b492a84a154dcc472b6085c6"}], "guid": "9765ef73a2a045bb016acc8828e954d1f14eedc5251924716035834dc4025efd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "9765ef73a2a045bb016acc8828e954d192fc5de22e9796de56986e921c13fc8d", "name": "MPhotos", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "9765ef73a2a045bb016acc8828e954d1bb5b5d55872da6012f23b693adfe3f44", "name": "MPhotos.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}