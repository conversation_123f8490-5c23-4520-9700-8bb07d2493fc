---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.5/SwiftUI.swiftmodule/arm64-apple-ios-simulator.swiftmodule'
dependencies:
  - mtime:           1746660471000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.5/SwiftUI.swiftmodule/arm64-apple-ios-simulator.swiftmodule'
    size:            3391880
  - mtime:           1745047443000000000
    path:            'usr/lib/swift/Swift.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1929404
    sdk_relative:    true
  - mtime:           1745048235000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1745048441000000000
    path:            'usr/lib/swift/_errno.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            3853
    sdk_relative:    true
  - mtime:           1745048462000000000
    path:            'usr/lib/swift/_signal.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1064
    sdk_relative:    true
  - mtime:           1745048462000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1065
    sdk_relative:    true
  - mtime:           1745048456000000000
    path:            'usr/lib/swift/_time.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1028
    sdk_relative:    true
  - mtime:           1745048456000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1476
    sdk_relative:    true
  - mtime:           1745048468000000000
    path:            'usr/lib/swift/unistd.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            816
    sdk_relative:    true
  - mtime:           1745048370000000000
    path:            'usr/lib/swift/_math.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            15247
    sdk_relative:    true
  - mtime:           1745047474000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            4224
    sdk_relative:    true
  - mtime:           1745048479000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            18218
    sdk_relative:    true
  - mtime:           1745049022000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            230593
    sdk_relative:    true
  - mtime:           1745049158000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            22870
    sdk_relative:    true
  - mtime:           1745049689000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            167797
    sdk_relative:    true
  - mtime:           1745043435000000000
    path:            'usr/include/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1745044333000000000
    path:            'usr/include/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1745209361000000000
    path:            'System/Library/Frameworks/Security.framework/Headers/Security.apinotes'
    size:            162
    sdk_relative:    true
  - mtime:           1745379238000000000
    path:            'usr/include/XPC.apinotes'
    size:            123
    sdk_relative:    true
  - mtime:           1745045809000000000
    path:            'System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes'
    size:            81098
    sdk_relative:    true
  - mtime:           1745047406000000000
    path:            'System/Library/Frameworks/CoreData.framework/Headers/CoreData.apinotes'
    size:            7789
    sdk_relative:    true
  - mtime:           1745049682000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            6560
    sdk_relative:    true
  - mtime:           1745049812000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            57133
    sdk_relative:    true
  - mtime:           1745049955000000000
    path:            'usr/lib/swift/CoreFoundation.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            22822
    sdk_relative:    true
  - mtime:           1745049983000000000
    path:            'usr/lib/swift/XPC.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            33617
    sdk_relative:    true
  - mtime:           1745049028000000000
    path:            'usr/lib/swift/Observation.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            3451
    sdk_relative:    true
  - mtime:           1745049689000000000
    path:            'usr/lib/swift/System.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            95467
    sdk_relative:    true
  - mtime:           1745050377000000000
    path:            'System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            991698
    sdk_relative:    true
  - mtime:           1745050540000000000
    path:            'System/Library/Frameworks/CoreData.framework/Modules/CoreData.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            42600
    sdk_relative:    true
  - mtime:           1745047476000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Headers/CoreGraphics.apinotes'
    size:            52901
    sdk_relative:    true
  - mtime:           1745050736000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Modules/CoreGraphics.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            53003
    sdk_relative:    true
  - mtime:           1745051351000000000
    path:            'System/Library/Frameworks/UniformTypeIdentifiers.framework/Headers/UniformTypeIdentifiers.apinotes'
    size:            1666
    sdk_relative:    true
  - mtime:           1745050455000000000
    path:            'usr/lib/swift/UniformTypeIdentifiers.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            20573
    sdk_relative:    true
  - mtime:           1745048235000000000
    path:            'usr/include/os.apinotes'
    size:            1658
    sdk_relative:    true
  - mtime:           1745049954000000000
    path:            'usr/lib/swift/os.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            108028
    sdk_relative:    true
  - mtime:           1745052499000000000
    path:            'System/Library/Frameworks/CoreTransferable.framework/Modules/CoreTransferable.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            22034
    sdk_relative:    true
  - mtime:           1745050730000000000
    path:            'System/Library/Frameworks/DeveloperToolsSupport.framework/Modules/DeveloperToolsSupport.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            10883
    sdk_relative:    true
  - mtime:           1745050738000000000
    path:            'usr/lib/swift/OSLog.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1261
    sdk_relative:    true
  - mtime:           1745049701000000000
    path:            'usr/lib/swift/simd.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            229468
    sdk_relative:    true
  - mtime:           1745049895000000000
    path:            'usr/lib/swift/Spatial.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            99178
    sdk_relative:    true
  - mtime:           1745050898000000000
    path:            'System/Library/Frameworks/OpenGLES.framework/Headers/OpenGLES.apinotes'
    size:            1192
    sdk_relative:    true
  - mtime:           1743191765000000000
    path:            'System/Library/Frameworks/Metal.framework/Headers/Metal.apinotes'
    size:            77528
    sdk_relative:    true
  - mtime:           1745044942000000000
    path:            'System/Library/Frameworks/CoreImage.framework/Headers/CoreImage.apinotes'
    size:            36883
    sdk_relative:    true
  - mtime:           1745051356000000000
    path:            'System/Library/Frameworks/CoreText.framework/Headers/CoreText.apinotes'
    size:            1662
    sdk_relative:    true
  - mtime:           1745046206000000000
    path:            'System/Library/Frameworks/QuartzCore.framework/Headers/QuartzCore.apinotes'
    size:            7428
    sdk_relative:    true
  - mtime:           1745051356000000000
    path:            'System/Library/Frameworks/UserNotifications.framework/Headers/UserNotifications.apinotes'
    size:            326
    sdk_relative:    true
  - mtime:           1745463577000000000
    path:            'System/Library/Frameworks/UIKit.framework/Headers/UIKit.apinotes'
    size:            162201
    sdk_relative:    true
  - mtime:           1745051180000000000
    path:            'System/Library/Frameworks/Accessibility.framework/Modules/Accessibility.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            21869
    sdk_relative:    true
  - mtime:           1745051622000000000
    path:            'System/Library/Frameworks/CoreText.framework/Modules/CoreText.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1544
    sdk_relative:    true
  - mtime:           1745050461000000000
    path:            'usr/lib/swift/DataDetection.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            699
    sdk_relative:    true
  - mtime:           1745050643000000000
    path:            'usr/lib/swift/Metal.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            25973
    sdk_relative:    true
  - mtime:           1745050770000000000
    path:            'usr/lib/swift/QuartzCore.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1772
    sdk_relative:    true
  - mtime:           1745051386000000000
    path:            'System/Library/Frameworks/Symbols.framework/Modules/Symbols.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            21559
    sdk_relative:    true
  - mtime:           1745051464000000000
    path:            'System/Library/Frameworks/FileProvider.framework/Modules/FileProvider.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1432
    sdk_relative:    true
  - mtime:           1745050786000000000
    path:            'usr/lib/swift/CoreImage.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            703
    sdk_relative:    true
  - mtime:           1745296087000000000
    path:            'System/Library/Frameworks/UIKit.framework/Modules/UIKit.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            219890
    sdk_relative:    true
  - mtime:           1745052614000000000
    path:            'System/Library/Frameworks/SwiftUICore.framework/Modules/SwiftUICore.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1005193
    sdk_relative:    true
  - mtime:           1745054962000000000
    path:            'System/Library/Frameworks/SwiftUI.framework/Modules/SwiftUI.swiftmodule/arm64-apple-ios-simulator.swiftinterface'
    size:            1322342
    sdk_relative:    true
version:         1
...
