import Foundation
import Photos
import UIKit

/// 照片数据管理器，负责从PhotoKit获取照片数据
class PhotoDataManager {
    
    static let shared = PhotoDataManager()
    
    private init() {}
    
    // MARK: - Properties
    
    /// 所有照片的获取结果
    private var allPhotosResult: PHFetchResult<PHAsset>?
    
    /// 图片管理器
    private let imageManager = PHCachingImageManager()
    
    // MARK: - Public Methods
    
    /// 获取所有照片
    /// - Returns: 照片获取结果
    func fetchAllPhotos() -> PHFetchResult<PHAsset> {
        let options = PHFetchOptions()
        
        // 按创建时间排序，最新的在前面（降序）
        options.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]
        
        // 不包含隐藏的资源
        options.includeHiddenAssets = false
        
        // 获取所有媒体类型（图片和视频）
        let result = PHAsset.fetchAssets(with: options)
        
        // 缓存结果
        allPhotosResult = result
        
        return result
    }
    
    /// 获取指定类型的照片
    /// - Parameter mediaType: 媒体类型
    /// - Returns: 照片获取结果
    func fetchAssets(with mediaType: PHAssetMediaType) -> PHFetchResult<PHAsset> {
        let options = PHFetchOptions()
        options.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]
        options.includeHiddenAssets = false
        
        return PHAsset.fetchAssets(with: mediaType, options: options)
    }
    
    /// 获取只包含图片的照片
    /// - Returns: 图片获取结果
    func fetchImages() -> PHFetchResult<PHAsset> {
        return fetchAssets(with: .image)
    }
    
    /// 获取只包含视频的照片
    /// - Returns: 视频获取结果
    func fetchVideos() -> PHFetchResult<PHAsset> {
        return fetchAssets(with: .video)
    }
    
    /// 获取Live Photos
    /// - Returns: Live Photos获取结果
    func fetchLivePhotos() -> PHFetchResult<PHAsset> {
        let options = PHFetchOptions()
        options.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]
        options.includeHiddenAssets = false
        options.predicate = NSPredicate(format: "(mediaSubtype & %d) != 0", PHAssetMediaSubtype.photoLive.rawValue)
        
        return PHAsset.fetchAssets(with: options)
    }
    
    /// 获取截图
    /// - Returns: 截图获取结果
    func fetchScreenshots() -> PHFetchResult<PHAsset> {
        let options = PHFetchOptions()
        options.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]
        options.includeHiddenAssets = false
        options.predicate = NSPredicate(format: "(mediaSubtype & %d) != 0", PHAssetMediaSubtype.photoScreenshot.rawValue)
        
        return PHAsset.fetchAssets(with: options)
    }
    
    /// 获取照片统计信息
    /// - Returns: 照片统计信息
    func getPhotoStatistics() -> PhotoStatistics {
        let allPhotos = fetchAllPhotos()
        let images = fetchImages()
        let videos = fetchVideos()
        let livePhotos = fetchLivePhotos()
        let screenshots = fetchScreenshots()
        
        return PhotoStatistics(
            totalCount: allPhotos.count,
            imageCount: images.count,
            videoCount: videos.count,
            livePhotoCount: livePhotos.count,
            screenshotCount: screenshots.count
        )
    }
    
    /// 请求缩略图
    /// - Parameters:
    ///   - asset: 照片资源
    ///   - targetSize: 目标尺寸
    ///   - completion: 完成回调
    /// - Returns: 请求ID，可用于取消请求
    @discardableResult
    func requestThumbnail(
        for asset: PHAsset,
        targetSize: CGSize,
        completion: @escaping (UIImage?) -> Void
    ) -> PHImageRequestID {
        let options = PHImageRequestOptions()
        options.deliveryMode = .fastFormat
        options.resizeMode = .fast
        options.isNetworkAccessAllowed = false // 不允许网络下载，确保快速响应
        
        return imageManager.requestImage(
            for: asset,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: options
        ) { image, _ in
            DispatchQueue.main.async {
                completion(image)
            }
        }
    }
    
    /// 请求高质量图片
    /// - Parameters:
    ///   - asset: 照片资源
    ///   - targetSize: 目标尺寸
    ///   - completion: 完成回调
    /// - Returns: 请求ID，可用于取消请求
    @discardableResult
    func requestHighQualityImage(
        for asset: PHAsset,
        targetSize: CGSize,
        completion: @escaping (UIImage?) -> Void
    ) -> PHImageRequestID {
        let options = PHImageRequestOptions()
        options.deliveryMode = .highQualityFormat
        options.resizeMode = .exact
        options.isNetworkAccessAllowed = true // 允许网络下载
        
        return imageManager.requestImage(
            for: asset,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: options
        ) { image, _ in
            DispatchQueue.main.async {
                completion(image)
            }
        }
    }
    
    /// 取消图片请求
    /// - Parameter requestID: 请求ID
    func cancelImageRequest(_ requestID: PHImageRequestID) {
        imageManager.cancelImageRequest(requestID)
    }
    
    /// 开始缓存指定范围的图片
    /// - Parameters:
    ///   - assets: 要缓存的照片资源数组
    ///   - targetSize: 目标尺寸
    func startCaching(for assets: [PHAsset], targetSize: CGSize) {
        let options = PHImageRequestOptions()
        options.deliveryMode = .fastFormat
        options.resizeMode = .fast
        options.isNetworkAccessAllowed = false
        
        imageManager.startCachingImages(
            for: assets,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: options
        )
    }
    
    /// 停止缓存指定范围的图片
    /// - Parameters:
    ///   - assets: 要停止缓存的照片资源数组
    ///   - targetSize: 目标尺寸
    func stopCaching(for assets: [PHAsset], targetSize: CGSize) {
        let options = PHImageRequestOptions()
        options.deliveryMode = .fastFormat
        options.resizeMode = .fast
        options.isNetworkAccessAllowed = false
        
        imageManager.stopCachingImages(
            for: assets,
            targetSize: targetSize,
            contentMode: .aspectFill,
            options: options
        )
    }
    
    /// 停止所有缓存
    func stopCachingAllImages() {
        imageManager.stopCachingImagesForAllAssets()
    }
}

// MARK: - Supporting Types

/// 照片统计信息
struct PhotoStatistics {
    let totalCount: Int
    let imageCount: Int
    let videoCount: Int
    let livePhotoCount: Int
    let screenshotCount: Int
    
    /// 格式化的统计信息描述
    var description: String {
        return "总计: \(totalCount), 图片: \(imageCount), 视频: \(videoCount), Live Photos: \(livePhotoCount), 截图: \(screenshotCount)"
    }
}
