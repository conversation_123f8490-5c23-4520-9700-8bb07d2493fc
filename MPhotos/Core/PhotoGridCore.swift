import Foundation
import Photos
import UIKit

/// 高性能图库核心模块
/// 专门为图库滚动性能优化，使用紧耦合设计减少抽象开销
class PhotoGridCore {
    
    // MARK: - Properties
    
    /// 图片管理器，直接持有避免依赖注入开销
    private let imageManager = PHCachingImageManager()
    
    /// 内存缓存，使用简单的key-value结构
    private let cache = NSCache<NSString, UIImage>()
    
    /// 当前的照片数据
    private var assets: PHFetchResult<PHAsset>?
    
    /// 缓存配置
    private let cacheCountLimit = 200  // 最多缓存200张图片
    private let cacheTotalCostLimit = 50 * 1024 * 1024  // 50MB内存限制
    
    /// 预加载范围（当前可见区域前后各预加载多少张）
    private let preloadRange = 20
    
    /// 当前正在预加载的资源
    private var preloadingAssets = Set<PHAsset>()
    
    // MARK: - Initialization
    
    init() {
        setupCache()
        setupImageManager()
    }
    
    // MARK: - Setup
    
    private func setupCache() {
        cache.countLimit = cacheCountLimit
        cache.totalCostLimit = cacheTotalCostLimit
        
        // 监听内存警告
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    private func setupImageManager() {
        // 配置缓存管理器的内存限制
        imageManager.allowsCachingHighQualityImages = false
    }
    
    // MARK: - Public Methods
    
    /// 设置照片数据源
    /// - Parameter assets: 照片获取结果
    func setAssets(_ assets: PHFetchResult<PHAsset>) {
        self.assets = assets
        
        // 清理旧的缓存
        cache.removeAllObjects()
        stopAllCaching()
    }
    
    /// 高性能缩略图加载（内联优化）
    /// - Parameters:
    ///   - asset: 照片资源
    ///   - size: 目标尺寸
    ///   - completion: 完成回调
    /// - Returns: 请求ID，可用于取消请求
    @inline(__always)
    func loadThumbnail(
        for asset: PHAsset,
        size: CGSize,
        completion: @escaping (UIImage?) -> Void
    ) -> PHImageRequestID {
        
        // 生成缓存key
        let cacheKey = generateCacheKey(for: asset, size: size)
        
        // 先检查内存缓存
        if let cachedImage = cache.object(forKey: cacheKey) {
            completion(cachedImage)
            return PHInvalidImageRequestID
        }
        
        // 配置请求选项
        let options = PHImageRequestOptions()
        options.deliveryMode = .fastFormat
        options.resizeMode = .fast
        options.isNetworkAccessAllowed = false
        options.isSynchronous = false
        
        // 发起异步请求
        return imageManager.requestImage(
            for: asset,
            targetSize: size,
            contentMode: .aspectFill,
            options: options
        ) { [weak self] image, info in
            
            // 缓存图片
            if let image = image {
                let cost = Int(size.width * size.height * 4) // 估算内存占用
                self?.cache.setObject(image, forKey: cacheKey, cost: cost)
            }
            
            completion(image)
        }
    }
    
    /// 同步加载缩略图（用于预加载）
    /// - Parameters:
    ///   - asset: 照片资源
    ///   - size: 目标尺寸
    /// - Returns: 图片，如果加载失败返回nil
    @inline(__always)
    func loadThumbnailSync(for asset: PHAsset, size: CGSize) -> UIImage? {
        let cacheKey = generateCacheKey(for: asset, size: size)
        
        // 检查缓存
        if let cachedImage = cache.object(forKey: cacheKey) {
            return cachedImage
        }
        
        let options = PHImageRequestOptions()
        options.deliveryMode = .fastFormat
        options.resizeMode = .fast
        options.isNetworkAccessAllowed = false
        options.isSynchronous = true
        
        var resultImage: UIImage?
        
        imageManager.requestImage(
            for: asset,
            targetSize: size,
            contentMode: .aspectFill,
            options: options
        ) { [weak self] image, _ in
            if let image = image {
                let cost = Int(size.width * size.height * 4)
                self?.cache.setObject(image, forKey: cacheKey, cost: cost)
                resultImage = image
            }
        }
        
        return resultImage
    }
    
    /// 开始预加载指定范围的图片
    /// - Parameters:
    ///   - visibleRange: 当前可见的索引范围
    ///   - targetSize: 目标尺寸
    func startPreloading(visibleRange: Range<Int>, targetSize: CGSize) {
        guard let assets = assets else { return }
        
        // 计算预加载范围
        let startIndex = max(0, visibleRange.lowerBound - preloadRange)
        let endIndex = min(assets.count, visibleRange.upperBound + preloadRange)
        
        var assetsToPreload: [PHAsset] = []
        
        for i in startIndex..<endIndex {
            let asset = assets.object(at: i)
            
            // 检查是否已经在缓存中
            let cacheKey = generateCacheKey(for: asset, size: targetSize)
            if cache.object(forKey: cacheKey) == nil && !preloadingAssets.contains(asset) {
                assetsToPreload.append(asset)
                preloadingAssets.insert(asset)
            }
        }
        
        // 开始预加载
        if !assetsToPreload.isEmpty {
            let options = PHImageRequestOptions()
            options.deliveryMode = .fastFormat
            options.resizeMode = .fast
            options.isNetworkAccessAllowed = false
            
            imageManager.startCachingImages(
                for: assetsToPreload,
                targetSize: targetSize,
                contentMode: .aspectFill,
                options: options
            )
        }
    }
    
    /// 停止预加载指定范围外的图片
    /// - Parameters:
    ///   - visibleRange: 当前可见的索引范围
    ///   - targetSize: 目标尺寸
    func stopPreloading(outsideRange visibleRange: Range<Int>, targetSize: CGSize) {
        guard let assets = assets else { return }
        
        let keepStartIndex = max(0, visibleRange.lowerBound - preloadRange)
        let keepEndIndex = min(assets.count, visibleRange.upperBound + preloadRange)
        
        var assetsToStopCaching: [PHAsset] = []
        
        // 找出需要停止缓存的资源
        for asset in preloadingAssets {
            if let index = findAssetIndex(asset) {
                if index < keepStartIndex || index >= keepEndIndex {
                    assetsToStopCaching.append(asset)
                }
            }
        }
        
        // 停止缓存
        if !assetsToStopCaching.isEmpty {
            let options = PHImageRequestOptions()
            options.deliveryMode = .fastFormat
            options.resizeMode = .fast
            
            imageManager.stopCachingImages(
                for: assetsToStopCaching,
                targetSize: targetSize,
                contentMode: .aspectFill,
                options: options
            )
            
            // 从预加载集合中移除
            for asset in assetsToStopCaching {
                preloadingAssets.remove(asset)
            }
        }
    }
    
    /// 停止所有缓存
    func stopAllCaching() {
        imageManager.stopCachingImagesForAllAssets()
        preloadingAssets.removeAll()
    }
    
    /// 清理内存缓存
    func clearMemoryCache() {
        cache.removeAllObjects()
    }
    
    // MARK: - Private Methods
    
    /// 生成缓存key
    /// - Parameters:
    ///   - asset: 照片资源
    ///   - size: 尺寸
    /// - Returns: 缓存key
    @inline(__always)
    private func generateCacheKey(for asset: PHAsset, size: CGSize) -> NSString {
        return "\(asset.localIdentifier)_\(Int(size.width))x\(Int(size.height))" as NSString
    }
    
    /// 查找资源在数据源中的索引
    /// - Parameter asset: 照片资源
    /// - Returns: 索引，如果未找到返回nil
    private func findAssetIndex(_ asset: PHAsset) -> Int? {
        guard let assets = assets else { return nil }
        
        for i in 0..<assets.count {
            if assets.object(at: i) == asset {
                return i
            }
        }
        return nil
    }
    
    /// 处理内存警告
    @objc private func handleMemoryWarning() {
        // 清理一半的缓存
        cache.removeAllObjects()
        
        // 停止所有预加载
        stopAllCaching()
        
        print("📱 PhotoGridCore: 收到内存警告，已清理缓存")
    }
    
    // MARK: - Deinitialization
    
    deinit {
        NotificationCenter.default.removeObserver(self)
        stopAllCaching()
    }
}
