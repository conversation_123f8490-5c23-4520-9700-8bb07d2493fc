import Foundation
import Photos
import UIKit

/// 简化版高性能图库核心模块
/// 只包含基础缓存功能，不包含预加载
class PhotoGridCoreSimple {
    
    // MARK: - Properties
    
    /// 图片管理器
    private let imageManager = PHCachingImageManager()
    
    /// 内存缓存
    private let cache = NSCache<NSString, UIImage>()
    
    // MARK: - Initialization
    
    init() {
        setupCache()
    }
    
    // MARK: - Setup
    
    private func setupCache() {
        // 设置较小的缓存限制
        cache.countLimit = 100  // 最多缓存100张图片
        cache.totalCostLimit = 30 * 1024 * 1024  // 30MB内存限制
        
        // 监听内存警告
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    // MARK: - Public Methods
    
    /// 高性能缩略图加载
    /// - Parameters:
    ///   - asset: 照片资源
    ///   - size: 目标尺寸
    ///   - completion: 完成回调
    /// - Returns: 请求ID，可用于取消请求
    func loadThumbnail(
        for asset: PHAsset,
        size: CGSize,
        completion: @escaping (UIImage?) -> Void
    ) -> PHImageRequestID {
        
        // 生成缓存key
        let cacheKey = generateCacheKey(for: asset, size: size)
        
        // 先检查内存缓存
        if let cachedImage = cache.object(forKey: cacheKey) {
            completion(cachedImage)
            return PHInvalidImageRequestID
        }
        
        // 配置请求选项
        let options = PHImageRequestOptions()
        options.deliveryMode = .fastFormat
        options.resizeMode = .fast
        options.isNetworkAccessAllowed = false
        options.isSynchronous = false
        
        // 发起异步请求
        return imageManager.requestImage(
            for: asset,
            targetSize: size,
            contentMode: .aspectFill,
            options: options
        ) { [weak self] image, info in
            
            // 缓存图片
            if let image = image {
                let cost = Int(size.width * size.height * 4) // 估算内存占用
                self?.cache.setObject(image, forKey: cacheKey, cost: cost)
            }
            
            completion(image)
        }
    }
    
    /// 清理内存缓存
    func clearMemoryCache() {
        cache.removeAllObjects()
    }
    
    // MARK: - Private Methods
    
    /// 生成缓存key
    /// - Parameters:
    ///   - asset: 照片资源
    ///   - size: 尺寸
    /// - Returns: 缓存key
    private func generateCacheKey(for asset: PHAsset, size: CGSize) -> NSString {
        return "\(asset.localIdentifier)_\(Int(size.width))x\(Int(size.height))" as NSString
    }
    
    /// 处理内存警告
    @objc private func handleMemoryWarning() {
        // 清理缓存
        cache.removeAllObjects()
        print("📱 PhotoGridCoreSimple: 收到内存警告，已清理缓存")
    }
    
    // MARK: - Deinitialization
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}
