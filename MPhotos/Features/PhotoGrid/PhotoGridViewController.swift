import UIKit
import Photos

/// 照片网格视图控制器
class PhotoGridViewController: UICollectionViewController {

    // MARK: - Properties

    /// 照片数据
    private var allPhotos: PHFetchResult<PHAsset>?

    /// 高性能图库核心
    private let photoGridCore = PhotoGridCore()

    /// 选中的索引路径集合
    private var selectedIndices = Set<IndexPath>()

    /// 缩略图尺寸
    private var thumbnailSize: CGSize = CGSize(width: 200, height: 200)

    /// 每行显示的列数
    private var columnsPerRow: Int = 3 {
        didSet {
            updateLayout()
        }
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupCollectionView()
        checkPermissionAndLoadData()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        calculateThumbnailSize()
    }
    
    override func viewWillTransition(to size: CGSize, with coordinator: UIViewControllerTransitionCoordinator) {
        super.viewWillTransition(to: size, with: coordinator)
        
        coordinator.animate(alongsideTransition: { _ in
            self.calculateThumbnailSize()
        })
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        title = "图库"
        view.backgroundColor = .systemBackground
        
        // 配置导航栏
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            title: "选择",
            style: .plain,
            target: self,
            action: #selector(toggleSelectionMode)
        )
    }
    
    private func setupCollectionView() {
        // 注册cell
        collectionView.register(PhotoCell.self, forCellWithReuseIdentifier: PhotoCell.identifier)
        
        // 配置布局
        let layout = UICollectionViewFlowLayout()
        layout.minimumInteritemSpacing = 2
        layout.minimumLineSpacing = 2
        collectionView.collectionViewLayout = layout
        
        // 配置collectionView
        collectionView.backgroundColor = .systemBackground
        collectionView.allowsMultipleSelection = true
        
        calculateThumbnailSize()
    }
    
    private func calculateThumbnailSize() {
        guard let layout = collectionView.collectionViewLayout as? UICollectionViewFlowLayout else {
            return
        }
        
        let availableWidth = view.bounds.width - view.safeAreaInsets.left - view.safeAreaInsets.right
        let spacing = layout.minimumInteritemSpacing * CGFloat(columnsPerRow - 1)
        let itemWidth = (availableWidth - spacing) / CGFloat(columnsPerRow)
        
        layout.itemSize = CGSize(width: itemWidth, height: itemWidth)
        
        // 更新缩略图尺寸，使用设备像素密度
        let scale = UIScreen.main.scale
        thumbnailSize = CGSize(width: itemWidth * scale, height: itemWidth * scale)
    }
    
    private func updateLayout() {
        calculateThumbnailSize()
        collectionView.collectionViewLayout.invalidateLayout()
    }
    
    // MARK: - Data Loading
    
    private func checkPermissionAndLoadData() {
        let permissionStatus = PermissionManager.shared.getCurrentPermissionStatus()
        
        switch permissionStatus {
        case .authorized, .limited:
            loadPhotoData()
        case .denied, .restricted:
            showPermissionAlert()
        case .notDetermined:
            requestPermission()
        }
    }
    
    private func loadPhotoData() {
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            let photos = PhotoDataManager.shared.fetchAllPhotos()

            DispatchQueue.main.async {
                self?.allPhotos = photos

                // 设置PhotoGridCore的数据源
                self?.photoGridCore.setAssets(photos)

                self?.collectionView.reloadData()

                if photos.count == 0 {
                    self?.showEmptyState()
                }
            }
        }
    }
    
    private func requestPermission() {
        PermissionManager.shared.requestPhotoLibraryPermission { [weak self] status in
            switch status {
            case .authorized, .limited:
                self?.loadPhotoData()
            case .denied, .restricted:
                self?.showPermissionAlert()
            case .notDetermined:
                break
            }
        }
    }
    
    private func showPermissionAlert() {
        PermissionManager.shared.showPermissionDeniedAlert(from: self)
    }
    
    private func showEmptyState() {
        // TODO: 显示空状态视图
        print("📷 相册中没有照片")
    }
    
    // MARK: - Actions
    
    @objc private func toggleSelectionMode() {
        // TODO: 实现选择模式切换
        print("🔄 切换选择模式")
    }
    
    // MARK: - UICollectionViewDataSource
    
    override func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return allPhotos?.count ?? 0
    }
    
    override func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: PhotoCell.identifier, for: indexPath) as! PhotoCell

        if let asset = allPhotos?.object(at: indexPath.item) {
            cell.configure(with: asset, targetSize: thumbnailSize, photoGridCore: photoGridCore)
            cell.isPhotoSelected = selectedIndices.contains(indexPath)
        }

        return cell
    }
    
    // MARK: - UICollectionViewDelegate
    
    override func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        // 暂时只是切换选择状态
        if selectedIndices.contains(indexPath) {
            selectedIndices.remove(indexPath)
        } else {
            selectedIndices.insert(indexPath)
        }
        
        // 更新cell状态
        if let cell = collectionView.cellForItem(at: indexPath) as? PhotoCell {
            cell.isPhotoSelected = selectedIndices.contains(indexPath)
        }
        
        print("📸 选中照片数量: \(selectedIndices.count)")
    }

    // MARK: - UIScrollViewDelegate (性能优化)

    override func scrollViewDidScroll(_ scrollView: UIScrollView) {
        updatePreloadingRange()
    }

    override func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        updatePreloadingRange()
    }

    override func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        if !decelerate {
            updatePreloadingRange()
        }
    }

    /// 更新预加载范围
    private func updatePreloadingRange() {
        guard let allPhotos = allPhotos, allPhotos.count > 0 else { return }

        // 获取当前可见的索引路径
        let visibleIndexPaths = collectionView.indexPathsForVisibleItems
        guard !visibleIndexPaths.isEmpty else { return }

        // 计算可见范围
        let visibleIndices = visibleIndexPaths.map { $0.item }.sorted()
        let visibleRange = visibleIndices.first!..<(visibleIndices.last! + 1)

        // 开始预加载
        photoGridCore.startPreloading(visibleRange: visibleRange, targetSize: thumbnailSize)

        // 停止预加载范围外的图片
        photoGridCore.stopPreloading(outsideRange: visibleRange, targetSize: thumbnailSize)
    }
}

// MARK: - UICollectionViewDelegateFlowLayout

extension PhotoGridViewController: UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let availableWidth = view.bounds.width - view.safeAreaInsets.left - view.safeAreaInsets.right
        let spacing = (collectionViewLayout as? UICollectionViewFlowLayout)?.minimumInteritemSpacing ?? 2
        let itemWidth = (availableWidth - spacing * CGFloat(columnsPerRow - 1)) / CGFloat(columnsPerRow)
        
        return CGSize(width: itemWidth, height: itemWidth)
    }
}
