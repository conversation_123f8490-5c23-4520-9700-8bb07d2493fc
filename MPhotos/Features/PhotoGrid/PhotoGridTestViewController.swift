import UIKit
import Photos

/// 照片网格测试视图控制器，用于验证照片数据获取功能
class PhotoGridTestViewController: UIViewController {
    
    // MARK: - UI Components
    
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let stackView = UIStackView()
    
    private let statisticsLabel = UILabel()
    private let statusLabel = UILabel()
    private let testButton = UIButton(type: .system)
    private let refreshButton = UIButton(type: .system)
    
    // MARK: - Properties
    
    private var allPhotos: PHFetchResult<PHAsset>?
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        checkPermissionAndLoadData()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        view.backgroundColor = .systemBackground
        title = "图库数据测试"
        
        // 配置滚动视图
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        contentView.translatesAutoresizingMaskIntoConstraints = false
        stackView.translatesAutoresizingMaskIntoConstraints = false
        
        // 配置堆栈视图
        stackView.axis = .vertical
        stackView.spacing = 20
        stackView.alignment = .fill
        stackView.distribution = .fill
        
        // 配置统计标签
        statisticsLabel.numberOfLines = 0
        statisticsLabel.textAlignment = .center
        statisticsLabel.font = .systemFont(ofSize: 16, weight: .medium)
        statisticsLabel.text = "正在加载照片数据..."
        
        // 配置状态标签
        statusLabel.numberOfLines = 0
        statusLabel.textAlignment = .center
        statusLabel.font = .systemFont(ofSize: 14)
        statusLabel.textColor = .secondaryLabel
        statusLabel.text = "等待权限检查..."
        
        // 配置测试按钮
        testButton.setTitle("测试照片加载", for: .normal)
        testButton.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        testButton.backgroundColor = .systemBlue
        testButton.setTitleColor(.white, for: .normal)
        testButton.layer.cornerRadius = 8
        testButton.addTarget(self, action: #selector(testPhotoLoading), for: .touchUpInside)
        
        // 配置刷新按钮
        refreshButton.setTitle("刷新数据", for: .normal)
        refreshButton.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        refreshButton.backgroundColor = .systemGreen
        refreshButton.setTitleColor(.white, for: .normal)
        refreshButton.layer.cornerRadius = 8
        refreshButton.addTarget(self, action: #selector(refreshData), for: .touchUpInside)
        
        // 添加视图层次
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(stackView)
        
        stackView.addArrangedSubview(statisticsLabel)
        stackView.addArrangedSubview(statusLabel)
        stackView.addArrangedSubview(testButton)
        stackView.addArrangedSubview(refreshButton)
    }
    
    private func setupConstraints() {
        NSLayoutConstraint.activate([
            // 滚动视图约束
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            
            // 内容视图约束
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            
            // 堆栈视图约束
            stackView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            stackView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -20),
            stackView.bottomAnchor.constraint(lessThanOrEqualTo: contentView.bottomAnchor, constant: -20),
            
            // 按钮高度约束
            testButton.heightAnchor.constraint(equalToConstant: 44),
            refreshButton.heightAnchor.constraint(equalToConstant: 44)
        ])
    }
    
    // MARK: - Data Loading
    
    private func checkPermissionAndLoadData() {
        let permissionStatus = PermissionManager.shared.getCurrentPermissionStatus()
        
        switch permissionStatus {
        case .authorized, .limited:
            loadPhotoData()
        case .denied, .restricted:
            statusLabel.text = "❌ 没有相册访问权限\n请在设置中开启权限后重试"
            statisticsLabel.text = "无法加载照片数据"
        case .notDetermined:
            statusLabel.text = "❓ 权限状态未确定\n请重新启动应用"
            statisticsLabel.text = "等待权限授权..."
        }
    }
    
    private func loadPhotoData() {
        statusLabel.text = "✅ 权限已授权，正在加载数据..."
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            let statistics = PhotoDataManager.shared.getPhotoStatistics()
            self?.allPhotos = PhotoDataManager.shared.fetchAllPhotos()
            
            DispatchQueue.main.async {
                self?.updateUI(with: statistics)
            }
        }
    }
    
    private func updateUI(with statistics: PhotoStatistics) {
        statisticsLabel.text = """
        📊 照片库统计信息
        
        总数量: \(statistics.totalCount)
        图片: \(statistics.imageCount)
        视频: \(statistics.videoCount)
        Live Photos: \(statistics.livePhotoCount)
        截图: \(statistics.screenshotCount)
        """
        
        if statistics.totalCount > 0 {
            statusLabel.text = "✅ 数据加载完成！\n点击下方按钮测试照片加载功能"
            testButton.isEnabled = true
        } else {
            statusLabel.text = "📷 相册中没有照片\n请添加一些照片后重试"
            testButton.isEnabled = false
        }
    }
    
    // MARK: - Actions
    
    @objc private func testPhotoLoading() {
        guard let allPhotos = allPhotos, allPhotos.count > 0 else {
            showAlert(title: "测试失败", message: "没有可用的照片进行测试")
            return
        }
        
        statusLabel.text = "🔄 正在测试照片加载..."
        testButton.isEnabled = false
        
        // 测试加载前5张照片的缩略图
        let testCount = min(5, allPhotos.count)
        var loadedCount = 0
        
        for i in 0..<testCount {
            let asset = allPhotos.object(at: i)
            
            PhotoDataManager.shared.requestThumbnail(
                for: asset,
                targetSize: CGSize(width: 200, height: 200)
            ) { [weak self] image in
                loadedCount += 1
                
                if loadedCount == testCount {
                    self?.statusLabel.text = "✅ 照片加载测试完成！\n成功加载 \(testCount) 张缩略图"
                    self?.testButton.isEnabled = true
                }
            }
        }
    }
    
    @objc private func refreshData() {
        checkPermissionAndLoadData()
    }
    
    // MARK: - Helper Methods
    
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}
