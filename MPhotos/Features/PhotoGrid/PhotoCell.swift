import UIKit
import Photos

/// 照片网格单元格
class PhotoCell: UICollectionViewCell {

    // MARK: - UI Components

    let imageView = UIImageView()
    let selectionOverlay = UIView()
    let selectionIndicator = UIView()
    let videoIndicator = UIImageView()
    let durationLabel = UILabel()
    
    // MARK: - Properties
    
    /// 当前的图片请求ID，用于取消请求
    private var currentRequestID: PHImageRequestID?
    
    /// 是否选中状态
    var isPhotoSelected: Bool = false {
        didSet {
            updateSelectionState()
        }
    }
    
    /// 照片资源
    private var asset: PHAsset?
    
    // MARK: - Lifecycle

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupConstraints()
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        
        // 取消当前的图片请求
        if let requestID = currentRequestID {
            PhotoDataManager.shared.cancelImageRequest(requestID)
            currentRequestID = nil
        }
        
        // 重置状态
        imageView.image = nil
        isPhotoSelected = false
        asset = nil
        videoIndicator.isHidden = true
        durationLabel.isHidden = true
        durationLabel.text = nil
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        // 添加所有子视图
        contentView.addSubview(imageView)
        contentView.addSubview(selectionOverlay)
        contentView.addSubview(selectionIndicator)
        contentView.addSubview(videoIndicator)
        contentView.addSubview(durationLabel)

        // 配置图片视图
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true

        // 配置选择覆盖层
        selectionOverlay.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        selectionOverlay.isHidden = true

        // 配置选择指示器
        selectionIndicator.backgroundColor = .systemBlue
        selectionIndicator.layer.cornerRadius = 12
        selectionIndicator.layer.borderWidth = 2
        selectionIndicator.layer.borderColor = UIColor.white.cgColor
        selectionIndicator.isHidden = true

        // 配置视频指示器
        videoIndicator.image = UIImage(systemName: "play.circle.fill")
        videoIndicator.tintColor = .white
        videoIndicator.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        videoIndicator.layer.cornerRadius = 12
        videoIndicator.isHidden = true

        // 配置时长标签
        durationLabel.textColor = .white
        durationLabel.font = .systemFont(ofSize: 12, weight: .medium)
        durationLabel.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        durationLabel.layer.cornerRadius = 4
        durationLabel.layer.masksToBounds = true
        durationLabel.textAlignment = .center
        durationLabel.isHidden = true
    }

    private func setupConstraints() {
        // 禁用自动布局转换
        imageView.translatesAutoresizingMaskIntoConstraints = false
        selectionOverlay.translatesAutoresizingMaskIntoConstraints = false
        selectionIndicator.translatesAutoresizingMaskIntoConstraints = false
        videoIndicator.translatesAutoresizingMaskIntoConstraints = false
        durationLabel.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            // 图片视图填满整个cell
            imageView.topAnchor.constraint(equalTo: contentView.topAnchor),
            imageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            imageView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            imageView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),

            // 选择覆盖层填满整个cell
            selectionOverlay.topAnchor.constraint(equalTo: contentView.topAnchor),
            selectionOverlay.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            selectionOverlay.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            selectionOverlay.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),

            // 选择指示器在右上角
            selectionIndicator.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8),
            selectionIndicator.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -8),
            selectionIndicator.widthAnchor.constraint(equalToConstant: 24),
            selectionIndicator.heightAnchor.constraint(equalToConstant: 24),

            // 视频指示器在左上角
            videoIndicator.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8),
            videoIndicator.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 8),
            videoIndicator.widthAnchor.constraint(equalToConstant: 24),
            videoIndicator.heightAnchor.constraint(equalToConstant: 24),

            // 时长标签在右下角
            durationLabel.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -8),
            durationLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -8),
            durationLabel.heightAnchor.constraint(equalToConstant: 20),
            durationLabel.widthAnchor.constraint(greaterThanOrEqualToConstant: 30)
        ])
    }
    
    private func updateSelectionState() {
        selectionOverlay.isHidden = !isPhotoSelected
        selectionIndicator.isHidden = !isPhotoSelected
        
        // 添加选择动画
        if isPhotoSelected {
            UIView.animate(withDuration: 0.2, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0.5) {
                self.selectionIndicator.transform = CGAffineTransform(scaleX: 1.1, y: 1.1)
            } completion: { _ in
                UIView.animate(withDuration: 0.1) {
                    self.selectionIndicator.transform = .identity
                }
            }
        }
    }
    
    // MARK: - Public Methods
    
    /// 配置单元格
    /// - Parameters:
    ///   - asset: 照片资源
    ///   - targetSize: 目标尺寸
    ///   - photoGridCore: 高性能图库核心
    func configure(with asset: PHAsset, targetSize: CGSize, photoGridCore: PhotoGridCoreSimple) {
        self.asset = asset

        // 配置视频相关UI
        if asset.mediaType == .video {
            videoIndicator.isHidden = false
            durationLabel.isHidden = false
            durationLabel.text = formatDuration(asset.duration)
        } else {
            videoIndicator.isHidden = true
            durationLabel.isHidden = true
        }

        // 使用PhotoGridCore请求缩略图
        currentRequestID = photoGridCore.loadThumbnail(
            for: asset,
            size: targetSize
        ) { [weak self] image in
            // 确保这是当前asset的图片
            guard self?.asset == asset else { return }
            self?.imageView.image = image
        }
    }
    
    /// 格式化视频时长
    /// - Parameter duration: 时长（秒）
    /// - Returns: 格式化的时长字符串
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        
        if minutes > 0 {
            return String(format: "%d:%02d", minutes, seconds)
        } else {
            return String(format: "0:%02d", seconds)
        }
    }
}

// MARK: - Static Properties

extension PhotoCell {

    /// 单元格标识符
    static let identifier = "PhotoCell"
}
