import Foundation
import Photos
import UIKit

/// 权限管理器，负责处理相册访问权限
class PermissionManager {
    
    static let shared = PermissionManager()
    
    private init() {}
    
    /// 权限状态枚举
    enum PermissionStatus {
        case authorized      // 已授权
        case denied         // 被拒绝
        case restricted     // 受限制
        case notDetermined  // 未决定
        case limited        // 限制访问（iOS 14+）
    }
    
    /// 权限请求结果回调
    typealias PermissionCompletion = (PermissionStatus) -> Void
    
    // MARK: - Public Methods
    
    /// 获取当前权限状态
    func getCurrentPermissionStatus() -> PermissionStatus {
        let status = PHPhotoLibrary.authorizationStatus()
        return convertPHAuthorizationStatus(status)
    }
    
    /// 请求相册访问权限
    /// - Parameter completion: 权限请求完成回调
    func requestPhotoLibraryPermission(completion: @escaping PermissionCompletion) {
        let currentStatus = PHPhotoLibrary.authorizationStatus()
        
        switch currentStatus {
        case .authorized:
            completion(.authorized)
        case .limited:
            completion(.limited)
        case .denied, .restricted:
            completion(convertPHAuthorizationStatus(currentStatus))
        case .notDetermined:
            // 首次请求权限
            PHPhotoLibrary.requestAuthorization { [weak self] status in
                DispatchQueue.main.async {
                    let permissionStatus = self?.convertPHAuthorizationStatus(status) ?? .denied
                    completion(permissionStatus)
                }
            }
        @unknown default:
            completion(.denied)
        }
    }
    
    /// 检查是否有相册访问权限
    func hasPhotoLibraryPermission() -> Bool {
        let status = getCurrentPermissionStatus()
        return status == .authorized || status == .limited
    }
    
    /// 显示权限被拒绝的提示弹窗
    /// - Parameter from: 展示弹窗的视图控制器
    func showPermissionDeniedAlert(from viewController: UIViewController) {
        let alert = UIAlertController(
            title: "需要相册访问权限",
            message: "MPhotos需要访问您的照片库来帮助您管理和优化照片。请在设置中开启相册访问权限。",
            preferredStyle: .alert
        )
        
        // 取消按钮
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        // 前往设置按钮
        alert.addAction(UIAlertAction(title: "前往设置", style: .default) { _ in
            self.openAppSettings()
        })
        
        viewController.present(alert, animated: true)
    }
    
    /// 显示限制访问的提示弹窗
    /// - Parameter from: 展示弹窗的视图控制器
    func showLimitedAccessAlert(from viewController: UIViewController) {
        let alert = UIAlertController(
            title: "照片访问受限",
            message: "您当前只允许访问部分照片。如需访问所有照片，请在设置中修改权限。",
            preferredStyle: .alert
        )
        
        // 继续使用按钮
        alert.addAction(UIAlertAction(title: "继续使用", style: .cancel))
        
        // 前往设置按钮
        alert.addAction(UIAlertAction(title: "前往设置", style: .default) { _ in
            self.openAppSettings()
        })
        
        viewController.present(alert, animated: true)
    }
    
    // MARK: - Private Methods
    
    /// 转换PHAuthorizationStatus到自定义PermissionStatus
    private func convertPHAuthorizationStatus(_ status: PHAuthorizationStatus) -> PermissionStatus {
        switch status {
        case .authorized:
            return .authorized
        case .denied:
            return .denied
        case .restricted:
            return .restricted
        case .notDetermined:
            return .notDetermined
        case .limited:
            return .limited
        @unknown default:
            return .denied
        }
    }
    
    /// 打开应用设置页面
    private func openAppSettings() {
        guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else {
            return
        }
        
        if UIApplication.shared.canOpenURL(settingsUrl) {
            UIApplication.shared.open(settingsUrl)
        }
    }
}

// MARK: - Extensions

extension PermissionManager {
    
    /// 在应用启动时检查并请求权限
    /// - Parameters:
    ///   - viewController: 用于显示权限提示的视图控制器
    ///   - completion: 权限处理完成回调
    func checkPermissionOnAppLaunch(from viewController: UIViewController, completion: @escaping PermissionCompletion) {
        let currentStatus = getCurrentPermissionStatus()
        
        switch currentStatus {
        case .authorized:
            completion(.authorized)
        case .limited:
            // 可以选择是否显示提示
            completion(.limited)
        case .denied, .restricted:
            showPermissionDeniedAlert(from: viewController)
            completion(currentStatus)
        case .notDetermined:
            requestPhotoLibraryPermission { [weak self] status in
                switch status {
                case .denied, .restricted:
                    self?.showPermissionDeniedAlert(from: viewController)
                case .limited:
                    self?.showLimitedAccessAlert(from: viewController)
                default:
                    break
                }
                completion(status)
            }
        }
    }
}
