import UIKit

class SceneDelegate: UIResponder, UIWindowSceneDelegate {

    var window: UIWindow?

    func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
        guard let windowScene = (scene as? UIWindowScene) else { return }

        window = UIWindow(windowScene: windowScene)
        let mainTabBarController = MainTabBarController()
        window?.rootViewController = mainTabBarController
        window?.makeKeyAndVisible()

        // 延迟一点时间后检查权限，确保界面已经显示
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.checkPhotoLibraryPermission(from: mainTabBarController)
        }
    }

    func sceneDidDisconnect(_ scene: UIScene) {
    }

    func sceneDidBecomeActive(_ scene: UIScene) {
    }

    func sceneWillResignActive(_ scene: UIScene) {
    }

    func sceneWillEnterForeground(_ scene: UIScene) {
    }

    func sceneDidEnterBackground(_ scene: UIScene) {
    }

    // MARK: - Private Methods

    /// 检查相册权限
    private func checkPhotoLibraryPermission(from viewController: UIViewController) {
        PermissionManager.shared.checkPermissionOnAppLaunch(from: viewController) { status in
            switch status {
            case .authorized:
                print("✅ 相册权限已授权")
            case .limited:
                print("⚠️ 相册权限受限")
            case .denied:
                print("❌ 相册权限被拒绝")
            case .restricted:
                print("🚫 相册权限受限制")
            case .notDetermined:
                print("❓ 相册权限未决定")
            }
        }
    }
}

