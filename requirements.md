# MPhotos 完整需求文档

## 1. 项目概述

### 1.1 项目背景
MPhotos是一个高性能的iOS照片管理应用，专注于照片管理和清理功能。使用AI agent进行全部开发，采用平衡的架构设计：核心功能紧耦合保证性能，辅助功能适度解耦便于维护。

### 1.2 目标用户
- iOS用户（iOS 16.0+）
- 存储空间紧张，需要清理照片的用户
- 重视隐私，希望数据本地处理的用户

### 1.3 核心价值
1. **极致性能**：媲美原生照片应用的加载速度
2. **平衡架构**：性能关键模块紧耦合，其他模块适度解耦
3. **隐私优先**：所有数据本地处理，不收集用户信息

### 1.4 架构设计理念
- **图库模块（高频操作）**：采用紧耦合设计，直接调用，减少抽象层级
- **相簿/工具模块（低频操作）**：适度解耦，便于独立开发和维护
- **避免过度工程**：不追求完美解耦，而是追求性能和可维护性的平衡

### 1.5 开发约束
- 使用AI agent开发，无人工开发者
- 第一版只实现图库和相簿功能
- 代码要易于AI理解和修改

## 2. 图库Tab详细需求

### 2.1 UI布局设计

#### 2.1.1 整体布局
- 顶部：透明导航栏（可看到下方照片）
- 内容区：照片网格，最新照片在底部
- 底部：Tab栏
- 右侧：时间轴快速定位条

#### 2.1.2 照片网格
- **布局模式**：3列、5列、10列（捏合手势切换）
- **排序方式**：最新在底部，向上滚动查看历史
- **分组显示**：按天分组，日期标签吸顶显示
- **略缩图**：正方形无圆角
- **间距设置**：
  - 3列：2pt间距
  - 5列：2pt间距
  - 10列：0间距

### 2.2 核心功能

#### 2.2.1 使用系统API的功能
```swift
// 这些功能直接使用PhotoKit API，无需自己实现：
- 获取所有照片：PHAsset.fetchAssets()
- 按日期排序：PHFetchOptions.sortDescriptors
- 获取缩略图：PHImageManager.requestImage()
- 批量删除：PHAssetChangeRequest.deleteAssets()
- EXIF信息：PHAsset的各种属性
- 文件大小：通过PHAssetResource获取
- 截图识别：PHAsset.mediaSubtypes.contains(.photoScreenshot)
- 最近删除：PHAssetCollection.fetchAssetCollections(with: .smartAlbum)
```

#### 2.2.2 需要自己实现的功能
- 批量选择管理（集成在PhotoGridViewController中）
- 滑动连续选择
- 列数切换动画
- 快速滚动时的性能优化
- 内存管理和缓存策略

### 2.3 手势操作

#### 2.3.1 完整手势列表
- **捏合缩放**：切换3/5/10列
- **单击**：选择模式下选择/取消选择，正常模式下查看大图
- **长按**：进入选择模式
- **滑动选择**：选择模式下滑动可连续选择
- **双击**：放大到点击位置
- **下拉**：关闭照片详情
- **左右滑动**：在详情模式下切换照片
- **右侧时间轴拖动**：快速跳转到特定日期
- **双击状态栏**：快速返回底部（最新照片）

### 2.4 性能优化

#### 2.4.1 内存管理策略
- 内存缓存限制：100MB（约500张中等质量图）
- 无磁盘缓存：利用系统Photos.app的缓存
- 内存警告响应：立即清理所有缓存
- 使用autoreleasepool处理批量操作

#### 2.4.2 图片加载优化
- 渐进式加载：先低质量后高质量
- 根据滚动速度调整加载策略
- 及时取消不可见cell的请求
- 使用PHCachingImageManager预加载

### 2.5 搜索和筛选

#### 2.5.1 基础筛选（使用系统API）
- 日期范围筛选
- 媒体类型（照片/视频/Live Photo）
- 文件大小筛选
- 收藏状态
- 截图识别

#### 2.5.2 搜索功能
- 多条件组合搜索
- 搜索历史记录（本地存储）
- 按应用来源分类（通过相册名判断）

## 3. 相簿Tab详细需求

### 3.1 UI设计

#### 3.1.1 相簿列表
- 两列网格布局
- 圆角矩形卡片（cornerRadius: 2pt）
- 显示封面（最新1张照片正方形略缩图）
- 显示相簿名称和照片数量

#### 3.1.2 系统相簿
- 固定顺序显示
- 包含：最近项目、个人收藏、视频、自拍、实况照片、人像模式、截屏、最近删除
- 使用系统图标

### 3.2 功能需求

#### 3.2.1 相簿管理
- 创建新相簿
- 重命名相簿
- 删除相簿（需二次确认）
- 添加/移除照片
- 相簿排序（手动/时间/名称）

#### 3.2.2 系统相簿
- 只读，不可编辑
- 实时更新内容
- 最近删除提供恢复功能

## 4. 性能指标

### 4.1 加载性能
- 冷启动：< 3秒显示界面并定位到底部
- 热启动：< 0.5秒
- 首屏加载：< 2秒显示最新50张照片

### 4.2 运行性能
- 滚动FPS：最低55fps，目标60fps
- 内存占用：正常< 200MB，峰值< 300MB
- 响应时间：点击< 50ms，手势实时响应

## 5. 导入导出功能

### 5.1 导入
- 从Files app导入
- 支持批量导入
- 支持的格式：JPEG、PNG、HEIF、GIF

### 5.2 导出
- 导出到Files app
- 格式转换（HEIF转JPEG）
- 批量导出支持
- 压缩选项

## 6. 数据管理

### 6.1 本地存储
- UserDefaults：用户偏好设置（< 1MB）
- Core Data：相簿信息、排序信息（< 5MB）
- 无图片缓存到磁盘
- 应用总存储目标：< 10MB

### 6.2 数据同步
- 相簿信息支持iCloud同步（可选）
- 设置信息本地存储

## 7. 错误处理

### 7.1 权限错误
- 无相册权限时显示引导界面
- 提供跳转到系统设置的快捷方式

### 7.2 系统错误
- 内存不足：自动清理缓存
- 加载失败：显示重试按钮
- 网络错误：不影响本地功能

## 8. 国际化支持

### 8.1 支持语言
- 简体中文
- 英语

### 8.2 本地化内容
- 所有UI文本
- 日期格式（跟随系统）
- 数字格式

## 9. 第一版实现范围

### 9.1 必须实现
- 图库基础浏览（最新在底部）
- 3/5/10列切换
- 批量选择和删除
- 基础相簿功能
- 内存优化和性能监控
- EXIF信息查看
- 文件大小筛选

### 9.2 可选实现
- 导入导出
- 搜索筛选
- 按应用来源分类

### 9.3 不实现（仅预留接口，且后续会有新功能）
- 相似照片检测
- 模糊照片检测

## 10. 未来功能规划

### 10.1 清理功能（第二版）
```swift
// 预留接口
protocol CodeRecognitionProtocol {
    func detectQRCodes(in image: UIImage) -> [String]
    func detectExpiredCodes(in photos: [PhotoModel]) -> [PhotoModel]
}

protocol SimilarityDetectionProtocol {
    func findSimilarPhotos(threshold: Float) -> [[PhotoModel]]
}
```

### 10.2 统计功能（第二版）
```swift
protocol StorageAnalyzerProtocol {
    func analyzeByMonth() -> [MonthlyStorage]
    func analyzeByType() -> [TypeStorage]
    func analyzeBySource() -> [SourceStorage]
}
```

## 11. UI/UX细节

### 11.1 动画效果
- 列数切换：0.25秒弹性动画
- 照片选中：0.1秒缩放动画
- 页面转场：标准iOS动画
- 加载显示：0.2秒淡入

### 11.2 视觉反馈
- 触觉反馈：选中时轻触，删除时警告
- 加载状态：浅灰色占位图
- 选中状态：蓝色勾选+半透明遮罩
- 空状态：图标+文字提示

### 11.3 辅助功能
- 支持VoiceOver
- 支持动态字体
- 支持减少动画
- 高对比度模式

## 12. 安全和隐私

### 12.1 数据安全
- 所有数据本地处理
- 不上传用户照片
- 不收集用户信息
- 支持应用锁（系统级）

### 12.2 隐私声明
- App Store隐私标签：不收集数据
- 相册权限仅用于本地管理
- 无网络传输用户内容

## 13. 测试要求

### 13.1 功能测试
- 所有核心功能可正常使用
- 各种边界情况处理正确
- 错误提示友好准确

### 13.2 性能测试
- 10000+照片库流畅浏览
- 快速滚动不卡顿
- 内存不泄露

### 13.3 兼容性测试
- iOS 16.0 - 17.x
- iPhone 8 - iPhone 15 Pro Max
- 横竖屏切换
