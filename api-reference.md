# iOS系统API使用指南

## PhotoKit Framework

### 基础类型
```swift
import Photos

// 核心类型
PHAsset          // 单个照片或视频
PHAssetCollection // 相簿
PHFetchResult    // 查询结果集
PHFetchOptions   // 查询选项
PHImageManager   // 图片加载管理器
PHCachingImageManager // 带缓存的图片管理器
```

### 权限管理

#### 请求权限
```swift
PHPhotoLibrary.requestAuthorization { status in
    switch status {
    case .authorized:
        // 已授权
    case .denied, .restricted:
        // 被拒绝
    case .notDetermined:
        // 未决定
    case .limited:
        // 限制访问（iOS 14+）
    @unknown default:
        break
    }
}
```

#### 检查权限状态
```swift
let status = PHPhotoLibrary.authorizationStatus()
```

### 获取照片

#### 获取所有照片
```swift
let options = PHFetchOptions()
options.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: true)]
options.includeHiddenAssets = false
let allPhotos = PHAsset.fetchAssets(with: options)
```

#### 获取指定类型
```swift
// 只获取图片
let images = PHAsset.fetchAssets(with: .image, options: options)

// 只获取视频
let videos = PHAsset.fetchAssets(with: .video, options: options)

// 获取Live Photos
options.predicate = NSPredicate(format: "(mediaSubtype & %d) != 0", PHAssetMediaSubtype.photoLive.rawValue)
let livePhotos = PHAsset.fetchAssets(with: options)
```

#### 获取截图
```swift
options.predicate = NSPredicate(format: "(mediaSubtype & %d) != 0", PHAssetMediaSubtype.photoScreenshot.rawValue)
let screenshots = PHAsset.fetchAssets(with: options)
```

### 加载图片

#### 基础加载
```swift
PHImageManager.default().requestImage(
    for: asset,
    targetSize: CGSize(width: 200, height: 200),
    contentMode: .aspectFill,
    options: nil
) { image, info in
    // 使用image
    let isDegraded = (info?[PHImageResultIsDegradedKey] as? Bool) ?? false
    let isFinished = !(isDegraded)
}
```

#### 高级选项
```swift
let options = PHImageRequestOptions()
options.deliveryMode = .opportunistic  // 渐进式加载
options.resizeMode = .fast            // 快速缩放
options.isSynchronous = false         // 异步加载
options.isNetworkAccessAllowed = false // 不下载iCloud照片
options.version = .current            // 当前版本（非原始）

let requestID = PHImageManager.default().requestImage(for: asset, targetSize: size, contentMode: .aspectFill, options: options) { image, info in
    // 处理图片
}

// 取消请求
PHImageManager.default().cancelImageRequest(requestID)
```

#### 使用缓存管理器
```swift
let cachingManager = PHCachingImageManager()

// 开始缓存
cachingManager.startCachingImages(
    for: assets,
    targetSize: thumbnailSize,
    contentMode: .aspectFill,
    options: nil
)

// 停止缓存
cachingManager.stopCachingImages(
    for: assets,
    targetSize: thumbnailSize,
    contentMode: .aspectFill,
    options: nil
)

// 停止所有缓存
cachingManager.stopCachingImagesForAllAssets()
```

### 获取照片元数据

#### 基础信息
```swift
let asset: PHAsset = ...

// 基础属性
asset.pixelWidth           // 宽度（像素）
asset.pixelHeight          // 高度（像素）
asset.creationDate         // 创建时间
asset.modificationDate     // 修改时间
asset.location            // GPS位置
asset.duration            // 时长（视频）
asset.isFavorite          // 是否收藏
asset.isHidden            // 是否隐藏
asset.mediaType           // 媒体类型
asset.mediaSubtypes       // 媒体子类型
```

#### 获取文件大小
```swift
let resources = PHAssetResource.assetResources(for: asset)
if let resource = resources.first {
    let unsignedInt64 = resource.value(forKey: "fileSize") as? CLong
    let fileSize = Int64(bitPattern: UInt64(unsignedInt64!))
    // fileSize 是字节数
}
```

#### 获取文件名
```swift
let resources = PHAssetResource.assetResources(for: asset)
let filename = resources.first?.originalFilename ?? "Unknown"
```

### 相簿管理

#### 获取系统相簿
```swift
// 所有智能相簿
let smartAlbums = PHAssetCollection.fetchAssetCollections(
    with: .smartAlbum,
    subtype: .any,
    options: nil
)

// 具体类型
let favorites = PHAssetCollection.fetchAssetCollections(
    with: .smartAlbum,
    subtype: .smartAlbumFavorites,
    options: nil
)

// 相簿子类型
.smartAlbumFavorites      // 个人收藏
.smartAlbumRecentlyAdded  // 最近添加
.smartAlbumVideos         // 视频
.smartAlbumSelfPortraits  // 自拍
.smartAlbumPanoramas      // 全景照片
.smartAlbumTimelapses     // 延时摄影
.smartAlbumSlomoVideos    // 慢动作
.smartAlbumBursts         // 连拍快照
.smartAlbumScreenshots    // 截屏
.smartAlbumLivePhotos     // 实况照片
```

#### 获取用户相簿
```swift
let userAlbums = PHAssetCollection.fetchAssetCollections(
    with: .album,
    subtype: .albumRegular,
    options: nil
)
```

#### 获取相簿中的照片
```swift
let assets = PHAsset.fetchAssets(in: assetCollection, options: fetchOptions)
```

### 修改操作

#### 创建相簿
```swift
PHPhotoLibrary.shared().performChanges({
    PHAssetCollectionChangeRequest.creationRequestForAssetCollection(withTitle: "新相簿")
}) { success, error in
    if success {
        // 创建成功
    }
}
```

#### 添加照片到相簿
```swift
PHPhotoLibrary.shared().performChanges({
    if let album = album,
       let addAssetRequest = PHAssetCollectionChangeRequest(for: album) {
        addAssetRequest.addAssets([asset] as NSArray)
    }
}) { success, error in
    // 处理结果
}
```

#### 删除照片
```swift
PHPhotoLibrary.shared().performChanges({
    PHAssetChangeRequest.deleteAssets([asset] as NSArray)
}) { success, error in
    // 处理结果
}
```

#### 收藏照片
```swift
PHPhotoLibrary.shared().performChanges({
    let request = PHAssetChangeRequest(for: asset)
    request.isFavorite = true
}) { success, error in
    // 处理结果
}
```

### 监听变化

```swift
class PhotoLibraryObserver: NSObject, PHPhotoLibraryChangeObserver {
    func photoLibraryDidChange(_ changeInstance: PHChange) {
        // 处理变化
        if let changes = changeInstance.changeDetails(for: fetchResult) {
            fetchResult = changes.fetchResultAfterChanges
            
            DispatchQueue.main.async {
                // 更新UI
                if changes.hasIncrementalChanges {
                    // 处理增量更新
                    collectionView.performBatchUpdates({
                        if let removed = changes.removedIndexes {
                            collectionView.deleteItems(at: removed.map { IndexPath(item: $0, section: 0) })
                        }
                        if let inserted = changes.insertedIndexes {
                            collectionView.insertItems(at: inserted.map { IndexPath(item: $0, section: 0) })
                        }
                        if let changed = changes.changedIndexes {
                            collectionView.reloadItems(at: changed.map { IndexPath(item: $0, section: 0) })
                        }
                    })
                } else {
                    collectionView.reloadData()
                }
            }
        }
    }
}

// 注册监听
PHPhotoLibrary.shared().register(observer)

// 取消监听
PHPhotoLibrary.shared().unregisterChangeObserver(observer)
```

## UIKit相关

### CollectionView优化

#### 预加载
```swift
func collectionView(_ collectionView: UICollectionView, prefetchItemsAt indexPaths: [IndexPath]) {
    // 预加载图片
    let assets = indexPaths.compactMap { getAsset(at: $0) }
    cachingManager.startCachingImages(for: assets, targetSize: thumbnailSize, contentMode: .aspectFill, options: nil)
}

func collectionView(_ collectionView: UICollectionView, cancelPrefetchingForItemsAt indexPaths: [IndexPath]) {
    // 取消预加载
    let assets = indexPaths.compactMap { getAsset(at: $0) }
    cachingManager.stopCachingImages(for: assets, targetSize: thumbnailSize, contentMode: .aspectFill, options: nil)
}
```

### 手势识别

#### 捏合手势
```swift
let pinch = UIPinchGestureRecognizer(target: self, action: #selector(handlePinch(_:)))
pinch.delegate = self
collectionView.addGestureRecognizer(pinch)

@objc func handlePinch(_ gesture: UIPinchGestureRecognizer) {
    if gesture.state == .changed {
        let scale = gesture.scale
        // 处理缩放
    }
}
```

#### 长按手势
```swift
let longPress = UILongPressGestureRecognizer(target: self, action: #selector(handleLongPress(_:)))
longPress.minimumPressDuration = 0.5
collectionView.addGestureRecognizer(longPress)
```

### 性能监控

#### FPS监控
```swift
class FPSMonitor {
    private var displayLink: CADisplayLink?
    private var lastTimestamp: CFTimeInterval = 0
    
    func start() {
        displayLink = CADisplayLink(target: self, selector: #selector(tick))
        displayLink?.add(to: .main, forMode: .common)
    }
    
    @objc private func tick(displayLink: CADisplayLink) {
        if lastTimestamp == 0 {
            lastTimestamp = displayLink.timestamp
            return
        }
        
        let fps = 1 / (displayLink.timestamp - lastTimestamp)
        lastTimestamp = displayLink.timestamp
        
        print("FPS: \(Int(fps))")
    }
}
```

## 内存管理

### 内存警告处理
```swift
override func didReceiveMemoryWarning() {
    super.didReceiveMemoryWarning()
    
    // 清理缓存
    imageCache.removeAllObjects()
    
    // 停止所有图片缓存
    cachingManager.stopCachingImagesForAllAssets()
    
    // 重新加载当前可见的
    let visibleIndexPaths = collectionView.indexPathsForVisibleItems
    collectionView.reloadItems(at: visibleIndexPaths)
}
```

### 自动释放池
```swift
// 处理大量图片时使用
for i in 0..<1000 {
    autoreleasepool {
        // 处理图片
        let image = processImage(at: i)
        // image会在这个作用域结束时释放
    }
}
```

## 文件操作

### 导出到Files
```swift
let documentPicker = UIDocumentPickerViewController(forExporting: [fileURL])
documentPicker.delegate = self
present(documentPicker, animated: true)
```

### 从Files导入
```swift
let documentPicker = UIDocumentPickerViewController(forOpeningContentTypes: [.image])
documentPicker.delegate = self
documentPicker.allowsMultipleSelection = true
present(documentPicker, animated: true)
```

## 最佳实践

1. **使用targetSize**: 总是指定合适的目标尺寸，避免加载原图
2. **复用PHImageRequestID**: 及时取消不需要的请求
3. **使用opportunistic模式**: 先显示低质量图，再显示高质量
4. **限制并发请求**: 同时请求数不超过20个
5. **监听内存警告**: 及时清理缓存
6. **使用PHCachingImageManager**: 预加载即将显示的图片
7. **避免主线程阻塞**: 图片处理放在后台队列